# Port Forwarding Setup Guide

This guide explains how to set up the ADC Data Validation UI with port forwarding for remote access.

## Overview

The application consists of two components:
1. **Backend API** (FastAPI) - Serves data and handles authentication
2. **Frontend UI** (React/Vite) - User interface that calls the backend API

When using port forwarding, you need to:
1. Start the backend and make it accessible via a forwarded port
2. Start the frontend with the correct backend URL

## Step-by-Step Setup

### 1. Start the Backend

First, start the backend server:

```bash
./start_backend.sh
```

The backend will start on `http://0.0.0.0:8000` and be accessible for port forwarding.

### 2. Set Up Port Forwarding for Backend

Forward the backend port to make it accessible from your external URL:

```bash
# Example: Forward local port 8000 to external URL
# This depends on your port forwarding setup (SSH tunnel, cloud provider, etc.)
```

Let's say your backend is now accessible at: `http://backend-hosted-url.com:8000`

### 3. Start the Frontend with Backend URL

Start the frontend and specify the backend URL:

```bash
./start_frontend.sh http://backend-hosted-url.com:8000
```

### 4. Set Up Port Forwarding for Frontend

The frontend will start on port 5173 (or 5174 if 5173 is busy). Forward this port to make the UI accessible:

```bash
# Forward frontend port for external access
```

## Script Usage

### Frontend Script Options

```bash
# Use default localhost backend
./start_frontend.sh

# Use custom backend URL
./start_frontend.sh http://backend-hosted-url.com:8000

# Use HTTPS backend
./start_frontend.sh https://api.example.com

# Show help
./start_frontend.sh --help
```

### What the Script Does

1. **Validates** the backend URL format
2. **Updates** the `.env` file with the specified backend URL
3. **Installs** dependencies if needed
4. **Displays** configuration information
5. **Starts** the Vite development server with proper host binding

## Configuration Details

### Backend Configuration

The backend is configured to:
- Bind to `0.0.0.0:8000` (accessible from external hosts)
- Allow CORS from multiple origins including `localhost:5173`

### Frontend Configuration

The frontend is configured to:
- Bind to `0.0.0.0:5173` (accessible from external hosts)
- Use the backend URL specified in the startup script
- Automatically update the `VITE_API_BASE_URL` environment variable

## Example Workflow

```bash
# 1. Start backend
./start_backend.sh

# 2. Set up port forwarding for backend (port 8000)
# Your backend is now at: http://your-domain.com:8000

# 3. Start frontend with the forwarded backend URL
./start_frontend.sh http://your-domain.com:8000

# 4. Set up port forwarding for frontend (port 5173)
# Your frontend is now at: http://your-domain.com:5173
```

## Troubleshooting

### Frontend Can't Connect to Backend

1. **Check the backend URL** in the frontend logs
2. **Verify CORS settings** in the backend `.env` file
3. **Test backend accessibility** directly: `curl http://your-backend-url/health`

### Port Already in Use

The Vite server will automatically try alternative ports (5174, 5175, etc.) if the default port is busy.

### Network Error

Ensure both services are bound to `0.0.0.0` and not just `localhost` or `127.0.0.1`.

## Environment Variables

### Backend (.env)
```bash
HOST=0.0.0.0
PORT=8000
ALLOWED_ORIGINS=http://localhost:5173,http://your-domain.com:5173
```

### Frontend (.env)
```bash
VITE_API_BASE_URL=http://your-backend-url:8000
VITE_DEV_SERVER_HOST=0.0.0.0
VITE_DEV_SERVER_PORT=5173
```

## Security Notes

- Use HTTPS in production environments
- Configure proper CORS origins for production
- Use secure authentication tokens
- Consider using environment-specific configuration files
