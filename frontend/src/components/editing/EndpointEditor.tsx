import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogContent,
  DialogActions,
  Button,
  TextField,

  Typography,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
} from '@mui/material';
import { Add, Delete } from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';

interface EndpointData {
  endpoint_name: string;
  endpoint_type: string;
  measurement_value: number | null;
  measurement_unit: string;
  measurement_error?: number;
  error_type?: string;
  statistical_significance?: string;
  dose_amount?: number;
  dose_unit?: string;
  administration_route?: string;
  treatment_schedule?: string;
  timepoint?: string;
  citations: string[];
}

interface EndpointEditorProps {
  open: boolean;
  onClose: () => void;
  onSave: (data: EndpointData) => void;
  initialData?: EndpointData;
  mode: 'create' | 'edit';
}

const EndpointEditor: React.FC<EndpointEditorProps> = ({
  open,
  onClose,
  onSave,
  initialData,
  mode,
}) => {
  const [citations, setCitations] = useState<string[]>(initialData?.citations || []);
  const [newCitation, setNewCitation] = useState('');

  const { control, handleSubmit, reset, formState: { errors } } = useForm<EndpointData>({
    defaultValues: initialData || {
      endpoint_name: '',
      endpoint_type: '',
      measurement_value: null,
      measurement_unit: '',
      measurement_error: undefined,
      error_type: '',
      statistical_significance: '',
      dose_amount: undefined,
      dose_unit: '',
      administration_route: '',
      treatment_schedule: '',
      timepoint: '',
      citations: [],
    },
  });

  const handleAddCitation = () => {
    if (newCitation.trim()) {
      setCitations([...citations, newCitation.trim()]);
      setNewCitation('');
    }
  };

  const handleRemoveCitation = (index: number) => {
    setCitations(citations.filter((_, i) => i !== index));
  };

  const onSubmit = (data: EndpointData) => {
    onSave({ ...data, citations });
    handleClose();
  };

  const handleClose = () => {
    reset();
    setCitations([]);
    setNewCitation('');
    onClose();
  };

  const endpointTypes = [
    'Efficacy',
    'Safety',
    'Pharmacokinetics',
    'Pharmacodynamics',
    'Biomarker',
    'Other',
  ];

  const errorTypes = ['SEM', 'SD', '95% CI', 'Range', 'Other'];
  const administrationRoutes = ['IV', 'IP', 'SC', 'PO', 'IM', 'Other'];

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {mode === 'create' ? 'Add New Endpoint' : 'Edit Endpoint'}
      </DialogTitle>
      <DialogContent>
        <Box component="form" sx={{ mt: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Controller
              name="endpoint_name"
              control={control}
              rules={{ required: 'Endpoint name is required' }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Endpoint Name"
                  fullWidth
                  error={!!errors.endpoint_name}
                  helperText={errors.endpoint_name?.message}
                />
              )}
            />
            <Controller
              name="endpoint_type"
              control={control}
              rules={{ required: 'Endpoint type is required' }}
              render={({ field }) => (
                <FormControl fullWidth error={!!errors.endpoint_type}>
                  <InputLabel>Endpoint Type</InputLabel>
                  <Select {...field} label="Endpoint Type">
                    {endpointTypes.map((type) => (
                      <MenuItem key={type} value={type}>
                        {type}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
            <Controller
              name="measurement_value"
              control={control}
              rules={{ required: 'Measurement value is required' }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Measurement Value"
                  type="number"
                  fullWidth
                  error={!!errors.measurement_value}
                  helperText={errors.measurement_value?.message}
                  inputProps={{ step: 'any' }}
                />
              )}
            />
            <Controller
              name="measurement_unit"
              control={control}
              rules={{ required: 'Measurement unit is required' }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Measurement Unit"
                  fullWidth
                  error={!!errors.measurement_unit}
                  helperText={errors.measurement_unit?.message}
                />
              )}
            />
            <Controller
              name="measurement_error"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Measurement Error"
                  type="number"
                  fullWidth
                  inputProps={{ step: 'any' }}
                />
              )}
            />
            <Controller
              name="error_type"
              control={control}
              render={({ field }) => (
                <FormControl fullWidth>
                  <InputLabel>Error Type</InputLabel>
                  <Select {...field} label="Error Type">
                    {errorTypes.map((type) => (
                      <MenuItem key={type} value={type}>
                        {type}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
            <Controller
              name="statistical_significance"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Statistical Significance"
                  fullWidth
                  placeholder="e.g., p < 0.05"
                />
              )}
            />
            <Controller
              name="dose_amount"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Dose Amount"
                  type="number"
                  fullWidth
                  inputProps={{ step: 'any' }}
                />
              )}
            />
            <Controller
              name="dose_unit"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Dose Unit"
                  fullWidth
                  placeholder="e.g., mg/kg"
                />
              )}
            />
            <Controller
              name="administration_route"
              control={control}
              render={({ field }) => (
                <FormControl fullWidth>
                  <InputLabel>Administration Route</InputLabel>
                  <Select {...field} label="Administration Route">
                    {administrationRoutes.map((route) => (
                      <MenuItem key={route} value={route}>
                        {route}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
            <Controller
              name="treatment_schedule"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Treatment Schedule"
                  fullWidth
                  placeholder="e.g., Q3W x 4"
                />
              )}
            />
            <Controller
              name="timepoint"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Timepoint"
                  fullWidth
                  placeholder="e.g., Day 21"
                />
              )}
            />

          {/* Citations Section */}
          <Box sx={{ mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              Citations
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
              <TextField
                label="Add Citation"
                value={newCitation}
                onChange={(e) => setNewCitation(e.target.value)}
                fullWidth
                size="small"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddCitation();
                  }
                }}
              />
              <Button
                variant="outlined"
                onClick={handleAddCitation}
                startIcon={<Add />}
                disabled={!newCitation.trim()}
              >
                Add
              </Button>
            </Box>
            <List dense>
              {citations.map((citation, index) => (
                <ListItem key={index}>
                  <ListItemText
                    primary={citation}
                    primaryTypographyProps={{ variant: 'body2' }}
                  />
                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      onClick={() => handleRemoveCitation(index)}
                      size="small"
                    >
                      <Delete />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Cancel</Button>
        <Button onClick={handleSubmit(onSubmit)} variant="contained">
          {mode === 'create' ? 'Add Endpoint' : 'Save Changes'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EndpointEditor;
