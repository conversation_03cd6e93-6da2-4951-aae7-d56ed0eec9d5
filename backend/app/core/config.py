"""
Configuration settings for the ADC Validation API
"""
import os
from pathlib import Path
from typing import List
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Settings:
    """Application settings loaded from environment variables"""

    # Application settings
    APP_NAME: str = os.getenv("APP_NAME", "ADC Data Validation API")
    APP_VERSION: str = os.getenv("APP_VERSION", "1.0.0")
    DEBUG: bool = os.getenv("DEBUG", "true").lower() == "true"
    RELOAD: bool = os.getenv("RELOAD", "true").lower() == "true"

    # Server configuration
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", "8000"))

    # Base paths
    WORKSPACE_ROOT = Path(os.getenv("WORKSPACE_ROOT", "/mnt/data/ADC_Data_Center/adc_user_interface"))
    DATA_ROOT = Path(os.getenv("DATA_ROOT", str(WORKSPACE_ROOT / "data")))
    PUBLICATIONS_DIR = Path(os.getenv("PUBLICATIONS_DIR", str(DATA_ROOT / "publications")))
    ANNOTATIONS_DIR = Path(os.getenv("ANNOTATIONS_DIR", str(DATA_ROOT / "annotations")))
    USERS_FILE = Path(os.getenv("USERS_FILE", str(DATA_ROOT / "users" / "users.json")))
    SAMPLE_DATA_DIR = Path(os.getenv("SAMPLE_DATA_DIR", str(WORKSPACE_ROOT / "sample_data")))

    # Derived paths
    BACKEND_DIR = WORKSPACE_ROOT / "backend"
    FRONTEND_DIR = WORKSPACE_ROOT / "frontend"

    # API URLs
    BACKEND_URL: str = os.getenv("BACKEND_URL", "http://localhost:8000")
    FRONTEND_URL: str = os.getenv("FRONTEND_URL", "http://localhost:5173")

    # Security settings
    SECRET_KEY: str = os.getenv("SECRET_KEY", "dev-secret-key-change-in-production")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "1440"))
    ALGORITHM: str = os.getenv("ALGORITHM", "HS256")

    # CORS settings
    ALLOWED_ORIGINS: List[str] = [
        origin.strip()
        for origin in os.getenv("ALLOWED_ORIGINS", "http://localhost:3000,http://127.0.0.1:3000,http://localhost:5173,http://127.0.0.1:5173").split(",")
    ]

    # Logging settings
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FORMAT: str = os.getenv("LOG_FORMAT", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")

    # File upload settings
    MAX_FILE_SIZE_MB: int = int(os.getenv("MAX_FILE_SIZE_MB", "100"))
    ALLOWED_FILE_TYPES: List[str] = [
        ext.strip()
        for ext in os.getenv("ALLOWED_FILE_TYPES", "json,md,txt,csv").split(",")
    ]

    # Cache settings
    CACHE_TTL_SECONDS: int = int(os.getenv("CACHE_TTL_SECONDS", "3600"))
    ENABLE_CACHE: bool = os.getenv("ENABLE_CACHE", "true").lower() == "true"

    @classmethod
    def ensure_directories(cls):
        """Create necessary directories in workspace"""
        cls.DATA_ROOT.mkdir(exist_ok=True)
        cls.PUBLICATIONS_DIR.mkdir(exist_ok=True)
        cls.ANNOTATIONS_DIR.mkdir(exist_ok=True)
        (cls.DATA_ROOT / "users").mkdir(exist_ok=True)

# Create settings instance
settings = Settings()

# Ensure directories exist
settings.ensure_directories()
