import json
import os
import argparse
from getpass import getpass
from passlib.context import CryptContext

USERS_FILE = os.path.join(os.path.dirname(__file__), '../data/users/users.json')
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def load_users():
    if not os.path.exists(USERS_FILE):
        return []
    with open(USERS_FILE, 'r', encoding='utf-8') as f:
        return json.load(f)

def save_users(users):
    os.makedirs(os.path.dirname(USERS_FILE), exist_ok=True)
    with open(USERS_FILE, 'w', encoding='utf-8') as f:
        json.dump(users, f, indent=2)

def list_users():
    users = load_users()
    print("\nCurrent users:")
    for user in users:
        print(f"- {user['username']} (role: {user['role']})")

def add_user():
    users = load_users()
    username = input("Enter username: ").strip()
    if any(u['username'] == username for u in users):
        print("User already exists.")
        return
    password = getpass("Enter password: ")
    password2 = getpass("Confirm password: ")
    if password != password2:
        print("Passwords do not match.")
        return
    role = input("Enter role (admin/annotator/viewer): ").strip()
    hashed_password = pwd_context.hash(password)
    users.append({"username": username, "password": hashed_password, "role": role})
    save_users(users)
    print(f"User '{username}' added.")

def remove_user():
    users = load_users()
    username = input("Enter username to remove: ").strip()
    new_users = [u for u in users if u['username'] != username]
    if len(new_users) == len(users):
        print("User not found.")
        return
    save_users(new_users)
    print(f"User '{username}' removed.")

def main():
    parser = argparse.ArgumentParser(description="Manage users in users.json")
    parser.add_argument('command', choices=['add', 'remove', 'list'], help="Action to perform")
    args = parser.parse_args()
    if args.command == 'add':
        add_user()
    elif args.command == 'remove':
        remove_user()
    elif args.command == 'list':
        list_users()

if __name__ == "__main__":
    main() 