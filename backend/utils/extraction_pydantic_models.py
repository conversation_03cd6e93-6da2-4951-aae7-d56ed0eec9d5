from typing import List, Optional, Literal, Dict, Type
from pydantic import BaseModel, Field
from enum import Enum

# ENUM CLASSES
class AntigenExpressionLevels(str, Enum):
    HIGH = "High Antigen Expression"
    LOW = "Low Antigen Expression"
    MODERATE = "Moderate Antigen Expression"
    NOT_SPECIFIED = "Not Clearly Specified"

class ConcentrationComponents(str, Enum):
    INTACT_ADC = "Intact ADC"
    FREE_PAYLOAD = "Free Payload"
    FREE_ANTIBODY = "Free Antibody"
    NOT_SPECIFIED = "Not Clearly Specified"

class ExperimentType(str, Enum):
    IN_VIVO = "In Vivo Studies"
    IN_VITRO = "In Vitro Studies"
    EX_VIVO = "Ex Vivo Studies"
    NOT_SPECIFIED = "Not Clearly Specified"

class ModelType(str, Enum):
    CELL_LINE = "Cell Line Model"
    CDX = "Cell Line-Derived Xenograft (CDX)"
    PDX = "Patient-Derived Xenograft (PDX)"
    ORGANOID = "Organoid Model"
    SYNGENEIC = "Syngeneic Model"
    TISSUE_SPECIMENS = "Tissue Specimens"
    TRANSGENIC = "Transgenic Model"
    NON_CELL_BASED = "Non-cell based Model"
    NOT_SPECIFIED = "Not Clearly Specified"

class LinkerType(str, Enum):
    CLEAVABLE = "Cleavable Linker"
    NON_CLEAVABLE = "Non-cleavable Linker"
    NOT_SPECIFIED = "Not Clearly Specified"

class AntibodyClonality(str, Enum):
    MONOCLONAL = "Monoclonal Antibody (mAb)"
    POLYCLONAL = "Polyclonal Antibody (pAb)"
    NOT_SPECIFIED = "Not Clearly Specified"

class AntibodySpecies(str, Enum):
    MURINE = "Murine"
    CHIMERIC = "Chimeric"
    HUMANIZED = "Humanized"
    NOT_SPECIFIED = "Not Clearly Specified"

class AntibodyIsotype(str, Enum):
    IGG = "IgG"
    IGM = "IgM"
    IGA = "IgA"
    IGE = "IgE"
    IGD = "IgD"
    NOT_SPECIFIED = "Not Clearly Specified"

class ConjugationMethod(str, Enum):
    LYSINE = "Lysine Conjugation"
    CYSTEINE = "Cysteine Conjugation"
    SITE_SPECIFIC = "Site-Specific Conjugation"
    ENZYMATIC = "Enzymatic Conjugation"
    NOT_SPECIFIED = "Not Clearly Specified"

class AnimalSpecies(str, Enum):
    MOUSE = "Mouse"
    RAT = "Rat"
    RABBIT = "Rabbit"
    NON_HUMAN_PRIMATE = "Non-human Primate"
    HUMAN = "Human"
    NOT_SPECIFIED = "Not Clearly Specified"

# Antibody Drug Conjugate Information
class AntibodyDrugConjugate(BaseModel):
    """Information about ADC: Antibody Drug Conjugate with supporting citations from literature"""
    citations: List[str] = Field(..., description="List of specific sentences from the research paper where the ADC information is extracted or interpreted, providing evidence for each attribute")

    # ADC Relevant Information
    adc_name: str = Field(..., description="The specific name, code, or identifier of the Antibody Drug Conjugate as reported in the literature")
    adc_identifier: str = Field(..., description="The specific name, code, or identifier of the Antibody Drug Conjugate as reported in the literature")
    drug_antibody_ratio: Optional[str] = Field(None, description="The average number of payload molecules conjugated per antibody molecule (DAR), typically expressed as a numerical value (e.g., '3.5', '2-4')")
    conjugation_method: Optional[ConjugationMethod] = Field(None, description="The chemical method used to conjugate the payload to the antibody (lysine, cysteine, site-specific, or enzymatic)")

    # Antibody Component
    antibody_name: str = Field(..., description="The specific name or designation of the monoclonal antibody component used in the ADC")
    antibody_clonality: AntibodyClonality = Field(..., description="The clonality classification of the antibody (monoclonal or polyclonal)")
    antibody_species: AntibodySpecies = Field(..., description="The species origin or engineering type of the antibody (murine, humanized, chimeric, or not clearly specified)")
    antibody_isotype: AntibodyIsotype = Field(..., description="The immunoglobulin isotype and subclass of the antibody (e.g., IgG, IgM, IgA, IgE, IgD)")

    # Payload Component
    payload_name: str = Field(..., description="The specific name or chemical designation of the cytotoxic payload molecule conjugated to the antibody")
    payload_mechanism_of_action: str = Field(..., description="The specific molecular mechanism by which the payload exerts its cytotoxic effect (e.g., DNA alkylation, tubulin polymerization inhibition, topoisomerase inhibition)")

    # Linker Component
    linker_name: str = Field(..., description="The specific chemical name or designation of the linker molecule that connects the antibody to the payload")
    linker_type: LinkerType = Field(..., description="The stability and cleavage characteristics of the linker (cleavable or non-cleavable)")

    # Target Antigen
    target_antigen: str = Field(..., description="The specific cell surface antigen or receptor targeted by the ADC antibody component")


# Experimental Model Information
class ExperimentalModel(BaseModel):
    """Information about experimental model used in preclinical studies for ADC evaluation with supporting citations"""
    citations: List[str] = Field(..., description="List of specific sentences from the research paper where the experimental model information is extracted or interpreted, providing evidence for each attribute")

    # Model Identification
    model_identifier: str = Field(..., description="The specific name, designation, or identifier of the experimental model used in the study (e.g., cell line name, xenograft model designation)")
    model_type: ModelType = Field(..., description="The category of experimental model used for ADC testing (cell line, CDX, PDX, organoid, syngeneic, etc.)")
    experiment_type: ExperimentType = Field(..., description="The experimental approach used to evaluate the ADC (in vitro, in vivo, or ex vivo studies)")

    # Disease Characteristics
    primary_cancer_type: str = Field(..., description="The primary anatomical site or organ system where the cancer originates (e.g., lung cancer, breast cancer, colorectal cancer)")
    cancer_subtype: Optional[str] = Field(None, description="The specific histological, molecular, or genetic subclassification within the primary cancer type that may influence treatment response and prognosis")

    # Experimental Context Information
    sample_size: Optional[str] = Field(None, description="The number of subjects, animals, or replicates used in the experimental group for statistical analysis.")
    study_duration: Optional[str] = Field(None, description="The total time period over which the experiment was conducted, typically expressed in days, weeks, or months")
    control_groups: Optional[str] = Field(None, description="Description of control groups used in the study (vehicle control, isotype control, positive control, etc.) for comparative analysis.")
    dosing_schedule: Optional[str] = Field(None, description="The frequency and timing of ADC administration (e.g., single dose, daily, weekly, every 3 days)")
    administration_route: Optional[str] = Field(None, description="The method of ADC delivery to the experimental model (intravenous, intraperitoneal, subcutaneous, oral, etc.)")

# Preclinical Endpoints Information

# Observation Models for Multiple Measurements

class TumorGrowthInhibitionObservation(BaseModel):
    """Single observation of tumor growth inhibition measurement"""
    measured_percentage: Optional[str] = Field(None, description="Percentage reduction in tumor growth compared to control group")
    measurement_timepoint: Optional[str] = Field(None, description="Time after ADC administration when tumor volume was measured, typically expressed in days or weeks")
    administered_dose: Optional[str] = Field(None, description="Specific ADC dose administered that resulted in this TGI measurement, typically expressed in mg/kg")
    baseline_tumor_volume: Optional[str] = Field(None, description="Initial tumor volume at treatment start for this measurement")
    final_tumor_volume: Optional[str] = Field(None, description="Tumor volume at the measurement timepoint")
    control_tumor_volume: Optional[str] = Field(None, description="Control group tumor volume at the same timepoint for comparison")

class AntiTumorActivityDoseObservation(BaseModel):
    """Single observation of effective ADC dose for anti-tumor activity"""
    effective_dose_amount: Optional[str] = Field(None, description="ADC dose amount demonstrated to produce significant anti-tumor activity, typically expressed in mg/kg")
    dosing_frequency: Optional[str] = Field(None, description="Frequency of ADC administration in this regimen (e.g., 'once weekly', 'every 3 days', 'single dose')")
    treatment_duration: Optional[str] = Field(None, description="Total duration of this treatment regimen (e.g., '21 days', '4 weeks', 'until progression')")
    efficacy_measure: Optional[str] = Field(None, description="Specific endpoint used to define anti-tumor activity for this dose (e.g., tumor regression, survival benefit, TGI percentage)")
    response_rate: Optional[str] = Field(None, description="Percentage of subjects showing the defined anti-tumor response at this dose")

class CytotoxicityFrequencyObservation(BaseModel):
    """Single observation of cytotoxicity measurement"""
    cytotoxicity_rate: Optional[str] = Field(None, description="Frequency or percentage of cytotoxic events observed in this measurement")
    measurement_timepoint: Optional[str] = Field(None, description="Time after ADC administration when cytotoxicity was assessed, typically expressed in hours or days")
    administered_dose: Optional[str] = Field(None, description="ADC dose that resulted in the observed cytotoxicity, typically expressed in mg/kg or nM")
    cytotoxicity_type: Optional[str] = Field(None, description="Specific type of cytotoxic effect observed (e.g., apoptosis, necrosis, cell cycle arrest)")
    severity_grade: Optional[str] = Field(None, description="Severity classification of the cytotoxic effects, if graded according to standard criteria")

class EC50Observation(BaseModel):
    """Single observation of EC50 measurement"""
    measured_concentration: Optional[str] = Field(None, description="The ADC concentration producing 50% of maximum response, typically expressed in nanomolar (nM) or micromolar (μM) units")
    measurement_timepoint: Optional[str] = Field(None, description="The specific time duration after ADC treatment when EC50 was assessed (e.g., 72 hours, 5 days)")
    response_parameter: Optional[str] = Field(None, description="The specific biological response measured for EC50 calculation (e.g., cell viability, proliferation inhibition, apoptosis induction)")
    administered_dose_range: Optional[str] = Field(None, description="The range of ADC concentrations tested to generate the dose-response curve, typically expressed in nM or μM")
    curve_fit_quality: Optional[str] = Field(None, description="Quality metrics for the dose-response curve fit (e.g., R², confidence intervals)")

class CmaxObservation(BaseModel):
    """Single observation of Cmax measurement"""
    measured_component: ConcentrationComponents = Field(..., description="The specific ADC component analyzed for maximum concentration (intact ADC, free payload, free antibody, or other components)")
    measured_concentration: Optional[str] = Field(None, description="The peak plasma concentration of the specified component, typically expressed in ng/mL or μg/mL")
    time_to_cmax: Optional[str] = Field(None, description="The time point at which maximum concentration was observed after ADC administration, typically expressed in hours or days")
    administered_dose: Optional[str] = Field(None, description="The ADC dose administered that resulted in the measured Cmax, typically expressed in mg/kg")
    sampling_timepoints: Optional[str] = Field(None, description="The blood sampling schedule used to determine Cmax (e.g., 'pre-dose, 1h, 4h, 24h, 72h post-dose')")

# Single Observation Endpoint Models (typically one measurement per ADC-model combination)
class AntigenExpressionPercentage(BaseModel):
    """Endpoint measurement of antigen expression as percentage of positive cells for ADC target validation with supporting citations"""
    citations: List[str] = Field(..., description="List of specific sentences from the research paper where the antigen expression percentage data is extracted or interpreted")
    measured_level: AntigenExpressionLevels = Field(..., description="Categorized level of target antigen expression: High (>75%), Moderate (25-75%), Low (<25%), or as explicitly stated in the literature")
    expression_percentage: Optional[str] = Field(None, description="Quantitative percentage of cells expressing the target antigen, if reported numerically")
    measurement_method: Optional[str] = Field(None, description="Specific analytical technique used to assess antigen expression percentage (e.g., flow cytometry, immunohistochemistry, Western blot, qPCR)")

class AntigenExpressionHScore(BaseModel):
    """Endpoint measurement of antigen expression using H-score or intensity-based scoring systems for ADC target validation with supporting citations"""
    citations: List[str] = Field(..., description="List of specific sentences from the research paper where the H-score or intensity-based antigen expression data is extracted or interpreted")
    measured_hscore: Optional[str] = Field(None, description="H-score value (0-300) representing semi-quantitative immunohistochemistry assessment of antigen expression intensity and distribution")
    intensity_score: Optional[str] = Field(None, description="Alternative intensity-based scoring if H-score is not used (e.g., 0-3+ scale, weak/moderate/strong)")
    measurement_method: Optional[str] = Field(None, description="Specific analytical technique used for quantitative assessment (e.g., immunohistochemistry with H-score, immunofluorescence intensity)")
    scoring_criteria: Optional[str] = Field(None, description="Specific criteria or scale used for quantitative assessment (e.g., H-score calculation method, intensity scale definition)")

# Multiple Observation Endpoint Models (for endpoints with measurements across time/dose/conditions)

class EC50_HalfMaximalEffectiveConcentration(BaseModel):
    """Endpoint measurement of half-maximal effective concentration (EC50) for ADC potency assessment with supporting citations"""
    citations: List[str] = Field(..., description="List of specific sentences from the research paper where the EC50 measurement data is extracted or interpreted")
    measurement_method: Optional[str] = Field(None, description="Primary assay used to determine EC50 (e.g., MTT assay, cell viability assay, ELISA-based dose-response)")
    observations: List[EC50Observation] = Field(..., description="List of individual EC50 measurements, allowing for multiple observations at different timepoints, cell lines, or response parameters")

class TumorGrowthInhibition(BaseModel):
    """Endpoint measurement of tumor growth inhibition (TGI) for ADC efficacy assessment with supporting citations"""
    citations: List[str] = Field(..., description="List of specific sentences from the research paper where the TGI measurement data is extracted or interpreted")
    measurement_method: Optional[str] = Field(None, description="Primary technique used to assess tumor volume (e.g., caliper measurement, imaging techniques like MRI/CT, bioluminescence)")
    observations: List[TumorGrowthInhibitionObservation] = Field(..., description="List of individual TGI measurements, allowing for multiple observations at different timepoints, doses, or conditions")

class Cmax_MaximumConcentration(BaseModel):
    """Endpoint measurement of maximum plasma concentration (Cmax) for ADC pharmacokinetic assessment with supporting citations"""
    citations: List[str] = Field(..., description="List of specific sentences from the research paper where the Cmax measurement data is extracted or interpreted")
    measurement_method: Optional[str] = Field(None, description="Primary analytical technique used to quantify component concentrations (e.g., ELISA, LC-MS/MS, immunoassay)")
    observations: List[CmaxObservation] = Field(..., description="List of individual Cmax measurements, allowing for multiple observations of different ADC components, doses, or study conditions")

class HalfLifePeriod(BaseModel):
    """Endpoint measurement of elimination half-life (t1/2) for ADC pharmacokinetic assessment with supporting citations"""
    citations: List[str] = Field(..., description="List of specific sentences from the research paper where the half-life measurement data is extracted or interpreted")
    measured_component: ConcentrationComponents = Field(..., description="The specific ADC component analyzed for half-life determination (intact ADC, free payload, free antibody, or not clearly specified)")
    measured_halflife: Optional[str] = Field(None, description="The time required for the plasma concentration of the specified component to decrease by 50%, typically expressed in hours or days")
    measurement_method: Optional[str] = Field(None, description="The analytical technique used to quantify component concentration over time (e.g., ELISA, LC-MS/MS, immunoassay)")
    administered_dose: Optional[str] = Field(None, description="The ADC dose administered for half-life determination, typically expressed in mg/kg")
    sampling_timepoints: Optional[str] = Field(None, description="The blood sampling schedule used to determine half-life (e.g., 'pre-dose, 1h, 4h, 24h, 72h, 168h post-dose')")
    elimination_phase: Optional[str] = Field(None, description="The specific pharmacokinetic phase analyzed for half-life calculation (e.g., terminal elimination phase, distribution phase)")

class Internalization(BaseModel):
    """Endpoint measurement of ADC cellular internalization for target engagement assessment with supporting citations"""
    citations: List[str] = Field(..., description="List of specific sentences from the research paper where the internalization measurement data is extracted or interpreted")
    measured_component: ConcentrationComponents = Field(..., description="The specific ADC component analyzed for internalization (intact ADC, free payload, free antibody, or not clearly specified)")
    internalization_percentage: Optional[str] = Field(None, description="The percentage of administered ADC component internalized by target cells within the specified timeframe")
    measurement_timepoint: Optional[str] = Field(None, description="The time after ADC exposure when internalization was assessed, typically expressed in hours or days")
    measurement_method: Optional[str] = Field(None, description="The technique used to assess internalization (e.g., flow cytometry, confocal microscopy, fluorescence microscopy, radiolabeling)")
    administered_dose: Optional[str] = Field(None, description="The ADC concentration or dose used for internalization studies, typically expressed in nM, μg/mL, or mg/kg")

class AntiTumorActivityDose(BaseModel):
    """Endpoint measurement of effective ADC dosing regimens for anti-tumor activity assessment with supporting citations"""
    citations: List[str] = Field(..., description="List of specific sentences from the research paper where the effective dose data is extracted or interpreted")
    observations: List[AntiTumorActivityDoseObservation] = Field(..., description="List of individual effective dose measurements, allowing for multiple dose regimens and their corresponding efficacy outcomes")

class CytotoxicityFrequency(BaseModel):
    """Endpoint measurement of cytotoxic effects and their frequency for ADC safety assessment with supporting citations"""
    citations: List[str] = Field(..., description="List of specific sentences from the research paper where the cytotoxicity data is extracted or interpreted")
    measurement_method: Optional[str] = Field(None, description="Primary technique used to assess cytotoxicity (e.g., MTT assay, LDH release, trypan blue exclusion, flow cytometry)")
    observations: List[CytotoxicityFrequencyObservation] = Field(..., description="List of individual cytotoxicity measurements, allowing for multiple observations at different timepoints, doses, or conditions")

class LethalDose(BaseModel):
    """Endpoint measurement of lethal dose levels for ADC safety and toxicity assessment with supporting citations"""
    citations: List[str] = Field(..., description="List of specific sentences from the research paper where the lethal dose data is extracted or interpreted")
    lethal_dose_amount: str = Field(..., description="The ADC dose amount that causes lethality in the specified percentage of the population, typically expressed in mg/kg")
    lethality_percentage: float = Field(..., description="The percentage of the experimental population for which the specified dose is lethal (e.g., 50 for LD50, 10 for LD10)")
    measurement_timepoint: Optional[str] = Field(None, description="The time period over which lethality was assessed after ADC administration (e.g., '14 days', '30 days')")
    cause_of_death: Optional[str] = Field(None, description="The primary cause or mechanism of death observed at the lethal dose, if reported")

# Define endpoint name literals
EndpointName = Literal[
    "ANTIGEN_EXPRESSION_PERCENTAGE",
    "ANTIGEN_EXPRESSION_HSCORE",
    "EC50_HALF_MAXIMAL_EFFECTIVE_CONCENTRATION",
    "TUMOR_GROWTH_INHIBITION",
    "CMAX_MAXIMUM_CONCENTRATION",
    "HALF_LIFE_PERIOD",
    "INTERNALIZATION",
    "ANTI_TUMOR_ACTIVITY_DOSE",
    "CYTOTOXICITY_FREQUENCY",
    "LETHAL_DOSE"
]

# Function to get the appropriate endpoint model class based on the endpoint name
def get_endpoint_model(endpoint_name: EndpointName) -> Type[BaseModel]:
    """Get the appropriate endpoint model class based on the endpoint name"""
    endpoint_map: Dict[EndpointName, Type[BaseModel]] = {
        "ANTIGEN_EXPRESSION_PERCENTAGE": AntigenExpressionPercentage,
        "ANTIGEN_EXPRESSION_HSCORE": AntigenExpressionHScore,
        "EC50_HALF_MAXIMAL_EFFECTIVE_CONCENTRATION": EC50_HalfMaximalEffectiveConcentration,
        "TUMOR_GROWTH_INHIBITION": TumorGrowthInhibition,
        "CMAX_MAXIMUM_CONCENTRATION": Cmax_MaximumConcentration,
        "HALF_LIFE_PERIOD": HalfLifePeriod,
        "INTERNALIZATION": Internalization,
        "ANTI_TUMOR_ACTIVITY_DOSE": AntiTumorActivityDose,
        "CYTOTOXICITY_FREQUENCY": CytotoxicityFrequency,
        "LETHAL_DOSE": LethalDose
    }
    return endpoint_map[endpoint_name]
