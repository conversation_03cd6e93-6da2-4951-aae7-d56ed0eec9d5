import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  Chip,
  FormGroup,
  FormControlLabel,
  Checkbox,
  Button,
  Divider,
} from '@mui/material';
import { ExpandMore, Save, Add } from '@mui/icons-material';
import type { PublicationData } from '../../store/publicationSlice';
import type { Annotation } from '../../store/annotationSlice';

interface SidebarProps {
  publication: PublicationData;
  annotation: Annotation | null;
  onAddADC?: () => void;
  onAddModel?: () => void;
  onAddEndpoint?: () => void;
  onSaveChanges?: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  publication,
  annotation,
  onAddADC,
  onAddModel,
  onAddEndpoint,
  onSaveChanges
}) => {
  const [citationFilters, setCitationFilters] = useState({
    adc: true,
    model: true,
    endpoint: true,
  });

  const handleFilterChange = (filter: keyof typeof citationFilters) => {
    setCitationFilters(prev => ({
      ...prev,
      [filter]: !prev[filter],
    }));
  };

  // Extract unique ADCs and models from publication data
  const uniqueADCs = React.useMemo(() => {
    const adcMap = new Map();
    publication.extraction_data.forEach((combo: any) => {
      const adc = combo.adc_data;
      if (!adcMap.has(adc.adc_identifier)) {
        adcMap.set(adc.adc_identifier, adc);
      }
    });
    return Array.from(adcMap.values());
  }, [publication.extraction_data]);

  const uniqueModels = React.useMemo(() => {
    const modelMap = new Map();
    publication.extraction_data.forEach((combo: any) => {
      const model = combo.experimental_model_data;
      if (!modelMap.has(model.model_identifier)) {
        modelMap.set(model.model_identifier, model);
      }
    });
    return Array.from(modelMap.values());
  }, [publication.extraction_data]);

  return (
    <Box sx={{ p: 2, height: '100%', overflow: 'auto' }}>
      <Typography variant="h6" gutterBottom>
        Navigation
      </Typography>

      {/* Publication Info */}
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMore />}>
          <Typography variant="subtitle1">Publication</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography variant="body2" sx={{ mb: 1 }}>
            {publication.title}
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            <Chip size="small" label={`${uniqueADCs.length} ADCs`} />
            <Chip size="small" label={`${uniqueModels.length} Models`} />
            <Chip size="small" label={`${publication.extraction_data.length} Combinations`} />
          </Box>
        </AccordionDetails>
      </Accordion>

      {/* ADC Selection */}
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMore />}>
          <Typography variant="subtitle1">ADCs ({uniqueADCs.length})</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <List dense>
            {uniqueADCs.map((adc: any, index: number) => (
              <ListItem key={index} sx={{ px: 0 }}>
                <ListItemText
                  primary={adc.adc_name}
                  secondary={`Target: ${adc.target_antigen}`}
                  primaryTypographyProps={{ variant: 'body2' }}
                  secondaryTypographyProps={{ variant: 'caption' }}
                />
              </ListItem>
            ))}
          </List>
        </AccordionDetails>
      </Accordion>

      {/* Model Selection */}
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMore />}>
          <Typography variant="subtitle1">Models ({uniqueModels.length})</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <List dense>
            {uniqueModels.map((model: any, index: number) => (
              <ListItem key={index} sx={{ px: 0 }}>
                <ListItemText
                  primary={model.model_identifier}
                  secondary={`${model.model_type} - ${model.primary_cancer_type}`}
                  primaryTypographyProps={{ variant: 'body2' }}
                  secondaryTypographyProps={{ variant: 'caption' }}
                />
              </ListItem>
            ))}
          </List>
        </AccordionDetails>
      </Accordion>

      {/* Citation Filters */}
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMore />}>
          <Typography variant="subtitle1">Citation Filters</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <FormGroup>
            <FormControlLabel
              control={
                <Checkbox
                  checked={citationFilters.adc}
                  onChange={() => handleFilterChange('adc')}
                  size="small"
                />
              }
              label="ADC Citations"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={citationFilters.model}
                  onChange={() => handleFilterChange('model')}
                  size="small"
                />
              }
              label="Model Citations"
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={citationFilters.endpoint}
                  onChange={() => handleFilterChange('endpoint')}
                  size="small"
                />
              }
              label="Endpoint Citations"
            />
          </FormGroup>
        </AccordionDetails>
      </Accordion>

      <Divider sx={{ my: 2 }} />

      {/* Action Buttons */}
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
        <Button
          variant="outlined"
          startIcon={<Add />}
          size="small"
          fullWidth
          onClick={onAddADC}
        >
          Add ADC
        </Button>
        <Button
          variant="outlined"
          startIcon={<Add />}
          size="small"
          fullWidth
          onClick={onAddModel}
        >
          Add Model
        </Button>
        <Button
          variant="outlined"
          startIcon={<Add />}
          size="small"
          fullWidth
          onClick={onAddEndpoint}
        >
          Add Endpoint
        </Button>
        <Button
          variant="contained"
          startIcon={<Save />}
          size="small"
          fullWidth
          color="primary"
          onClick={onSaveChanges}
        >
          Save Changes
        </Button>
      </Box>

      {/* Annotation Status */}
      {annotation && (
        <Box sx={{ mt: 2, p: 1, bgcolor: 'grey.100', borderRadius: 1 }}>
          <Typography variant="caption" color="textSecondary">
            Status: {annotation.status}
          </Typography>
          <br />
          <Typography variant="caption" color="textSecondary">
            Annotator: {annotation.annotator_username}
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default Sidebar;
