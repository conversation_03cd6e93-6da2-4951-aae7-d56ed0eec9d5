"""
File system operations for JSON data storage
"""
import json
import aiofiles
from pathlib import Path
from typing import List, Dict, Any, Optional
from app.core.config import settings

class FileService:
    """Service for file-based data operations"""
    
    @staticmethod
    async def read_json_file(file_path: Path) -> Optional[Dict[str, Any]]:
        """Read JSON file asynchronously"""
        try:
            if not file_path.exists():
                return None
            
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                content = await f.read()
                return json.loads(content)
        except Exception as e:
            print(f"Error reading file {file_path}: {e}")
            return None
    
    @staticmethod
    async def write_json_file(file_path: Path, data: Dict[str, Any]) -> bool:
        """Write JSON file asynchronously"""
        try:
            # Ensure directory exists
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(data, indent=2, ensure_ascii=False))
            return True
        except Exception as e:
            print(f"Error writing file {file_path}: {e}")
            return False
    
    @staticmethod
    async def read_text_file(file_path: Path) -> Optional[str]:
        """Read text file asynchronously"""
        try:
            if not file_path.exists():
                return None
            
            async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
                return await f.read()
        except Exception as e:
            print(f"Error reading text file {file_path}: {e}")
            return None
    
    @staticmethod
    async def list_files(directory: Path, pattern: str = "*.json") -> List[Path]:
        """List files in directory matching pattern"""
        try:
            if not directory.exists():
                return []
            
            return list(directory.glob(pattern))
        except Exception as e:
            print(f"Error listing files in {directory}: {e}")
            return []

# Create service instance
file_service = FileService()
