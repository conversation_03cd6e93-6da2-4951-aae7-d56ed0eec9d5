import React, { useMemo } from 'react';
import { Box, Typography, Paper } from '@mui/material';
import ReactMarkdown from 'react-markdown';
import type { PublicationData } from '../../store/publicationSlice';
import type { Annotation } from '../../store/annotationSlice';

interface PublicationViewerProps {
  publication: PublicationData;
  annotation: Annotation | null;
}

const PublicationViewer: React.FC<PublicationViewerProps> = ({ publication }) => {
  // Extract all citations from the publication data
  const allCitations = useMemo(() => {
    const citations: Array<{
      text: string;
      type: 'adc' | 'model' | 'endpoint';
      entityId: string;
      data: any;
    }> = [];

    publication.extraction_data.forEach((combination: any, combIndex: number) => {
      // ADC citations
      combination.adc_data.citations?.forEach((citation: string) => {
        citations.push({
          text: citation,
          type: 'adc',
          entityId: `adc-${combIndex}`,
          data: combination.adc_data,
        });
      });

      // Model citations
      combination.experimental_model_data.citations?.forEach((citation: string) => {
        citations.push({
          text: citation,
          type: 'model',
          entityId: `model-${combIndex}`,
          data: combination.experimental_model_data,
        });
      });

      // Endpoint citations
      combination.endpoints?.forEach((endpoint: any, endpointIndex: number) => {
        endpoint.endpoint_data.citations?.forEach((citation: string) => {
          citations.push({
            text: citation,
            type: 'endpoint',
            entityId: `endpoint-${combIndex}-${endpointIndex}`,
            data: endpoint.endpoint_data,
          });
        });
      });
    });

    return citations;
  }, [publication.extraction_data]);

  // Highlight citations in the text
  const highlightedText = useMemo(() => {
    if (!publication.fulltext_markdown) return '';

    let text = publication.fulltext_markdown;

    // Sort citations by length (longest first) to avoid partial replacements
    const sortedCitations = [...allCitations].sort((a, b) => b.text.length - a.text.length);

    sortedCitations.forEach((citation, index) => {
      const citationIndex = text.indexOf(citation.text);
      if (citationIndex !== -1) {
        const before = text.substring(0, citationIndex);
        const after = text.substring(citationIndex + citation.text.length);

        const highlightClass = `citation-${citation.type}`;
        const highlightedCitation = `<span class="${highlightClass}" data-citation-id="${citation.entityId}" data-citation-index="${index}" title="Click to edit ${citation.type} data">${citation.text}</span>`;

        text = before + highlightedCitation + after;
      }
    });

    return text;
  }, [publication.fulltext_markdown, allCitations]);

  const getCitationStyles = () => {
    return `
      .citation-adc {
        background-color: #E3F2FD;
        padding: 2px 4px;
        border-radius: 3px;
        cursor: pointer;
        border: 1px solid transparent;
      }
      .citation-adc:hover {
        background-color: #BBDEFB;
        border-color: #2196F3;
      }
      .citation-model {
        background-color: #E8F5E8;
        padding: 2px 4px;
        border-radius: 3px;
        cursor: pointer;
        border: 1px solid transparent;
      }
      .citation-model:hover {
        background-color: #C8E6C9;
        border-color: #4CAF50;
      }
      .citation-endpoint {
        background-color: #FFF3E0;
        padding: 2px 4px;
        border-radius: 3px;
        cursor: pointer;
        border: 1px solid transparent;
      }
      .citation-endpoint:hover {
        background-color: #FFE0B2;
        border-color: #FF9800;
      }
    `;
  };

  return (
    <Box sx={{ p: 3, height: '100%', overflow: 'auto' }}>
      <style>{getCitationStyles()}</style>

      <Typography variant="h5" gutterBottom>
        {publication.title}
      </Typography>

      <Paper sx={{ p: 3, mt: 2 }}>
        {publication.fulltext_markdown ? (
          <Box
            sx={{
              '& h1, & h2, & h3, & h4, & h5, & h6': {
                mt: 3,
                mb: 2,
              },
              '& p': {
                mb: 2,
                lineHeight: 1.6,
              },
              '& ul, & ol': {
                mb: 2,
                pl: 3,
              },
            }}
          >
            <ReactMarkdown
              components={{
                // Custom renderer to handle HTML in markdown
                p: ({ children, ...props }) => (
                  <Typography
                    component="p"
                    variant="body1"
                    {...props}
                    dangerouslySetInnerHTML={
                      typeof children === 'string'
                        ? { __html: children }
                        : undefined
                    }
                  >
                    {typeof children !== 'string' ? children : undefined}
                  </Typography>
                ),
              }}
            >
              {highlightedText}
            </ReactMarkdown>
          </Box>
        ) : (
          <Typography variant="body1" color="textSecondary">
            No full text available for this publication.
          </Typography>
        )}
      </Paper>

      {/* Citation Legend */}
      <Paper sx={{ p: 2, mt: 2 }}>
        <Typography variant="h6" gutterBottom>
          Citation Legend
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Box
              sx={{
                width: 20,
                height: 20,
                backgroundColor: '#E3F2FD',
                border: '1px solid #2196F3',
                borderRadius: 1,
              }}
            />
            <Typography variant="body2">ADC Citations</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Box
              sx={{
                width: 20,
                height: 20,
                backgroundColor: '#E8F5E8',
                border: '1px solid #4CAF50',
                borderRadius: 1,
              }}
            />
            <Typography variant="body2">Model Citations</Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Box
              sx={{
                width: 20,
                height: 20,
                backgroundColor: '#FFF3E0',
                border: '1px solid #FF9800',
                borderRadius: 1,
              }}
            />
            <Typography variant="body2">Endpoint Citations</Typography>
          </Box>
        </Box>
      </Paper>
    </Box>
  );
};

export default PublicationViewer;
