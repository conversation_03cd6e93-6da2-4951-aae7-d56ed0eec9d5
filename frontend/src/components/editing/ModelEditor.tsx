import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogT<PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  TextField,

  Typography,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
} from '@mui/material';
import { Add, Delete } from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';

interface ModelData {
  model_identifier: string;
  model_type: string;
  primary_cancer_type: string;
  cancer_subtype?: string;
  species: string;
  strain?: string;
  sex?: string;
  age_range?: string;
  implantation_site?: string;
  cell_line_name?: string;
  passage_number?: number;
  citations: string[];
}

interface ModelEditorProps {
  open: boolean;
  onClose: () => void;
  onSave: (data: ModelData) => void;
  initialData?: ModelData;
  mode: 'create' | 'edit';
}

const ModelEditor: React.FC<ModelEditorProps> = ({
  open,
  onClose,
  onSave,
  initialData,
  mode,
}) => {
  const [citations, setCitations] = useState<string[]>(initialData?.citations || []);
  const [newCitation, setNewCitation] = useState('');

  const { control, handleSubmit, reset, formState: { errors } } = useForm<ModelData>({
    defaultValues: initialData || {
      model_identifier: '',
      model_type: '',
      primary_cancer_type: '',
      cancer_subtype: '',
      species: '',
      strain: '',
      sex: '',
      age_range: '',
      implantation_site: '',
      cell_line_name: '',
      passage_number: undefined,
      citations: [],
    },
  });

  const handleAddCitation = () => {
    if (newCitation.trim()) {
      setCitations([...citations, newCitation.trim()]);
      setNewCitation('');
    }
  };

  const handleRemoveCitation = (index: number) => {
    setCitations(citations.filter((_, i) => i !== index));
  };

  const onSubmit = (data: ModelData) => {
    onSave({ ...data, citations });
    handleClose();
  };

  const handleClose = () => {
    reset();
    setCitations([]);
    setNewCitation('');
    onClose();
  };

  const modelTypes = [
    'Cell line xenograft',
    'Patient-derived xenograft',
    'Syngeneic',
    'Transgenic',
    'Cell line',
    'Primary culture',
    'Other',
  ];

  const species = ['Human', 'Mouse', 'Rat', 'Other'];


  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {mode === 'create' ? 'Add New Model' : 'Edit Model'}
      </DialogTitle>
      <DialogContent>
        <Box component="form" sx={{ mt: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Controller
              name="model_identifier"
              control={control}
              rules={{ required: 'Model identifier is required' }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Model Identifier"
                  fullWidth
                  error={!!errors.model_identifier}
                  helperText={errors.model_identifier?.message}
                />
              )}
            />
            <Controller
              name="model_type"
              control={control}
              rules={{ required: 'Model type is required' }}
              render={({ field }) => (
                <FormControl fullWidth error={!!errors.model_type}>
                  <InputLabel>Model Type</InputLabel>
                  <Select {...field} label="Model Type">
                    {modelTypes.map((type) => (
                      <MenuItem key={type} value={type}>
                        {type}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
            <Controller
              name="primary_cancer_type"
              control={control}
              rules={{ required: 'Primary cancer type is required' }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Primary Cancer Type"
                  fullWidth
                  error={!!errors.primary_cancer_type}
                  helperText={errors.primary_cancer_type?.message}
                />
              )}
            />
            <Controller
              name="species"
              control={control}
              rules={{ required: 'Species is required' }}
              render={({ field }) => (
                <FormControl fullWidth error={!!errors.species}>
                  <InputLabel>Species</InputLabel>
                  <Select {...field} label="Species">
                    {species.map((spec) => (
                      <MenuItem key={spec} value={spec}>
                        {spec}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              )}
            />
            <Controller
              name="cell_line_name"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Cell Line Name"
                  fullWidth
                />
              )}
            />

          {/* Citations Section */}
          <Box sx={{ mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              Citations
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
              <TextField
                label="Add Citation"
                value={newCitation}
                onChange={(e) => setNewCitation(e.target.value)}
                fullWidth
                size="small"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddCitation();
                  }
                }}
              />
              <Button
                variant="outlined"
                onClick={handleAddCitation}
                startIcon={<Add />}
                disabled={!newCitation.trim()}
              >
                Add
              </Button>
            </Box>
            <List dense>
              {citations.map((citation, index) => (
                <ListItem key={index}>
                  <ListItemText
                    primary={citation}
                    primaryTypographyProps={{ variant: 'body2' }}
                  />
                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      onClick={() => handleRemoveCitation(index)}
                      size="small"
                    >
                      <Delete />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Cancel</Button>
        <Button onClick={handleSubmit(onSubmit)} variant="contained">
          {mode === 'create' ? 'Add Model' : 'Save Changes'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ModelEditor;
