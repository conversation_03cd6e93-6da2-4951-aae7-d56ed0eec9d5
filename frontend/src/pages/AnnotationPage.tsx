import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  Container,
  Paper,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
} from '@mui/material';
import type { RootState, AppDispatch } from '../store';
import { fetchPublication } from '../store/publicationSlice';
import { fetchAnnotation } from '../store/annotationSlice';
import Header from '../components/common/Header';
import Sidebar from '../components/sidebar/Sidebar';
import PublicationViewer from '../components/content/PublicationViewer';
import DataTable from '../components/visualization/DataTable';
import ADCEditor from '../components/editing/ADCEditor';
import ModelEditor from '../components/editing/ModelEditor';
import EndpointEditor from '../components/editing/EndpointEditor';

const AnnotationPage: React.FC = () => {
  const { publicationId } = useParams<{ publicationId: string }>();
  const dispatch = useDispatch<AppDispatch>();

  const { currentPublication, isLoading: publicationLoading, error: publicationError } = useSelector(
    (state: RootState) => state.publication
  );
  const { currentAnnotation, isLoading: annotationLoading, error: annotationError } = useSelector(
    (state: RootState) => state.annotation
  );

  // Local state for tabs and editors
  const [activeTab, setActiveTab] = useState(0);
  const [adcEditorOpen, setAdcEditorOpen] = useState(false);
  const [modelEditorOpen, setModelEditorOpen] = useState(false);
  const [endpointEditorOpen, setEndpointEditorOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);
  const [editMode, setEditMode] = useState<'create' | 'edit'>('create');

  useEffect(() => {
    if (publicationId) {
      dispatch(fetchPublication(publicationId));
      dispatch(fetchAnnotation(publicationId));
    }
  }, [dispatch, publicationId]);

  const isLoading = publicationLoading || annotationLoading;
  const error = publicationError || annotationError;

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <>
        <Header />
        <Container maxWidth="lg" sx={{ mt: 4 }}>
          <Alert severity="error">{error}</Alert>
        </Container>
      </>
    );
  }

  // Data processing functions
  const getUniqueADCs = () => {
    if (!currentPublication) return [];
    const adcMap = new Map();
    currentPublication.extraction_data.forEach((combo: any) => {
      const adc = combo.adc_data;
      if (!adcMap.has(adc.adc_identifier)) {
        adcMap.set(adc.adc_identifier, adc);
      }
    });
    return Array.from(adcMap.values());
  };

  const getUniqueModels = () => {
    if (!currentPublication) return [];
    const modelMap = new Map();
    currentPublication.extraction_data.forEach((combo: any) => {
      const model = combo.experimental_model_data;
      if (!modelMap.has(model.model_identifier)) {
        modelMap.set(model.model_identifier, model);
      }
    });
    return Array.from(modelMap.values());
  };

  const getAllEndpoints = () => {
    if (!currentPublication) return [];
    const endpoints: any[] = [];
    currentPublication.extraction_data.forEach((combo: any, comboIndex: number) => {
      combo.endpoints?.forEach((endpoint: any, endpointIndex: number) => {
        endpoints.push({
          ...endpoint.endpoint_data,
          comboIndex,
          endpointIndex,
          adc_name: combo.adc_data.adc_name,
          model_identifier: combo.experimental_model_data.model_identifier,
        });
      });
    });
    return endpoints;
  };

  // Editor handlers
  const handleEditADC = (adc: any) => {
    setEditingItem(adc);
    setEditMode('edit');
    setAdcEditorOpen(true);
  };

  const handleEditModel = (model: any) => {
    setEditingItem(model);
    setEditMode('edit');
    setModelEditorOpen(true);
  };

  const handleEditEndpoint = (endpoint: any) => {
    setEditingItem(endpoint);
    setEditMode('edit');
    setEndpointEditorOpen(true);
  };

  const handleSaveADC = (data: any) => {
    console.log('Saving ADC:', data);
    // TODO: Implement save functionality
  };

  const handleSaveModel = (data: any) => {
    console.log('Saving Model:', data);
    // TODO: Implement save functionality
  };

  const handleSaveEndpoint = (data: any) => {
    console.log('Saving Endpoint:', data);
    // TODO: Implement save functionality
  };

  const handleAddADC = () => {
    setEditingItem(null);
    setEditMode('create');
    setAdcEditorOpen(true);
  };

  const handleAddModel = () => {
    setEditingItem(null);
    setEditMode('create');
    setModelEditorOpen(true);
  };

  const handleAddEndpoint = () => {
    setEditingItem(null);
    setEditMode('create');
    setEndpointEditorOpen(true);
  };

  const handleSaveChanges = () => {
    console.log('Saving all changes...');
    // TODO: Implement save all functionality
  };

  // Table column definitions
  const adcColumns = [
    { id: 'adc_name', label: 'ADC Name', minWidth: 150 },
    { id: 'adc_identifier', label: 'Identifier', minWidth: 120 },
    { id: 'target_antigen', label: 'Target Antigen', minWidth: 120 },
    { id: 'antibody_name', label: 'Antibody', minWidth: 120 },
    { id: 'payload_name', label: 'Payload', minWidth: 120 },
    { id: 'citations', label: 'Citations', minWidth: 100, sortable: false },
  ];

  const modelColumns = [
    { id: 'model_identifier', label: 'Model ID', minWidth: 120 },
    { id: 'model_type', label: 'Type', minWidth: 150 },
    { id: 'primary_cancer_type', label: 'Cancer Type', minWidth: 150 },
    { id: 'species', label: 'Species', minWidth: 100 },
    { id: 'cell_line_name', label: 'Cell Line', minWidth: 120 },
    { id: 'citations', label: 'Citations', minWidth: 100, sortable: false },
  ];

  const endpointColumns = [
    { id: 'endpoint_name', label: 'Endpoint', minWidth: 150 },
    { id: 'endpoint_type', label: 'Type', minWidth: 120 },
    { id: 'measurement_value', label: 'Value', minWidth: 100 },
    { id: 'measurement_unit', label: 'Unit', minWidth: 80 },
    { id: 'adc_name', label: 'ADC', minWidth: 120 },
    { id: 'model_identifier', label: 'Model', minWidth: 120 },
    { id: 'citations', label: 'Citations', minWidth: 100, sortable: false },
  ];

  if (!currentPublication) {
    return (
      <>
        <Header />
        <Container maxWidth="lg" sx={{ mt: 4 }}>
          <Typography variant="h6">Publication not found</Typography>
        </Container>
      </>
    );
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 0:
        return <PublicationViewer publication={currentPublication} annotation={currentAnnotation} />;
      case 1:
        return (
          <DataTable
            title="ADCs"
            columns={adcColumns}
            data={getUniqueADCs()}
            onEdit={handleEditADC}
          />
        );
      case 2:
        return (
          <DataTable
            title="Experimental Models"
            columns={modelColumns}
            data={getUniqueModels()}
            onEdit={handleEditModel}
          />
        );
      case 3:
        return (
          <DataTable
            title="Endpoints"
            columns={endpointColumns}
            data={getAllEndpoints()}
            onEdit={handleEditEndpoint}
          />
        );
      default:
        return null;
    }
  };

  return (
    <>
      <Header />
      <Box sx={{ display: 'flex', height: 'calc(100vh - 64px)' }}>
        <Paper
          sx={{
            width: 300,
            flexShrink: 0,
            borderRadius: 0,
            borderRight: 1,
            borderColor: 'divider',
          }}
        >
          <Sidebar
            publication={currentPublication}
            annotation={currentAnnotation}
            onAddADC={handleAddADC}
            onAddModel={handleAddModel}
            onAddEndpoint={handleAddEndpoint}
            onSaveChanges={handleSaveChanges}
          />
        </Paper>
        <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
          <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
            <Tabs value={activeTab} onChange={(_, newValue) => setActiveTab(newValue)}>
              <Tab label="Publication Text" />
              <Tab label="ADCs" />
              <Tab label="Models" />
              <Tab label="Endpoints" />
            </Tabs>
          </Box>
          <Box sx={{ p: 2 }}>
            {renderTabContent()}
          </Box>
        </Box>
      </Box>

      {/* Editors */}
      <ADCEditor
        open={adcEditorOpen}
        onClose={() => setAdcEditorOpen(false)}
        onSave={handleSaveADC}
        initialData={editMode === 'edit' ? editingItem : undefined}
        mode={editMode}
      />
      <ModelEditor
        open={modelEditorOpen}
        onClose={() => setModelEditorOpen(false)}
        onSave={handleSaveModel}
        initialData={editMode === 'edit' ? editingItem : undefined}
        mode={editMode}
      />
      <EndpointEditor
        open={endpointEditorOpen}
        onClose={() => setEndpointEditorOpen(false)}
        onSave={handleSaveEndpoint}
        initialData={editMode === 'edit' ? editingItem : undefined}
        mode={editMode}
      />
    </>
  );
};

export default AnnotationPage;
