import api from './api';

export interface PublicationSummary {
  publication_id: string;
  title: string;
  adc_count: number;
  model_count: number;
  combination_count: number;
}

export interface PublicationData {
  publication_id: string;
  title: string;
  fulltext_markdown?: string;
  extraction_data: any[];
}

class PublicationService {
  async getPublications(token: string): Promise<PublicationSummary[]> {
    const response = await api.get('/api/publications/', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  }

  async getPublication(publicationId: string, token: string): Promise<PublicationData> {
    const response = await api.get(`/api/publications/${publicationId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  }

  async getPublicationADCs(publicationId: string, token: string): Promise<any[]> {
    const response = await api.get(`/api/publications/${publicationId}/adcs`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  }

  async getPublicationModels(publicationId: string, token: string): Promise<any[]> {
    const response = await api.get(`/api/publications/${publicationId}/models`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  }

  async getPublicationFulltext(publicationId: string, token: string): Promise<{ fulltext_markdown: string }> {
    const response = await api.get(`/api/publications/${publicationId}/fulltext`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  }
}

export const publicationService = new PublicationService();
