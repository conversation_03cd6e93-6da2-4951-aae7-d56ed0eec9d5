# Environment Configuration Guide

This document describes how to configure the ADC Data Validation UI using environment variables for both backend and frontend components.

## Overview

The application now uses environment variables for all configuration, making it easy to deploy in different environments (development, staging, production) without code changes.

## Backend Configuration

### Environment File Location
- **Development**: `backend/.env`
- **Example**: `backend/.env.example`

### Key Configuration Variables

#### Application Settings
```bash
APP_NAME=ADC Data Validation API
APP_VERSION=1.0.0
DEBUG=true
RELOAD=true
```

#### Server Configuration
```bash
HOST=0.0.0.0
PORT=8000
```

#### File Paths
```bash
WORKSPACE_ROOT=/mnt/data/ADC_Data_Center/adc_user_interface
DATA_ROOT=/path/to/data
PUBLICATIONS_DIR=/path/to/publications
ANNOTATIONS_DIR=/path/to/annotations
USERS_FILE=/path/to/users.json
SAMPLE_DATA_DIR=/path/to/sample_data
```

#### API URLs
```bash
BACKEND_URL=http://localhost:8000
FRONTEND_URL=http://localhost:5173
```

#### Security Settings
```bash
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=1440
ALGORITHM=HS256
```

#### CORS Settings
```bash
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:5173
```

## Frontend Configuration

### Environment File Location
- **Development**: `frontend/.env`
- **Example**: `frontend/.env.example`

### Key Configuration Variables

#### Application Settings
```bash
VITE_APP_NAME=ADC Data Validation UI
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Expert validation interface for ADC data extraction
```

#### API Configuration
```bash
VITE_API_BASE_URL=http://localhost:8000
VITE_API_TIMEOUT=30000
```

#### Authentication Settings
```bash
VITE_TOKEN_STORAGE_KEY=adc_validation_token
VITE_TOKEN_REFRESH_THRESHOLD=300000
```

#### UI Configuration
```bash
VITE_DEFAULT_PAGE_SIZE=10
VITE_MAX_PAGE_SIZE=100
VITE_DEBOUNCE_DELAY=300
```

#### Feature Flags
```bash
VITE_ENABLE_DARK_MODE=true
VITE_ENABLE_EXPORT=true
VITE_ENABLE_COLLABORATION=false
VITE_ENABLE_ANALYTICS=false
```

## Setup Instructions

### 1. Backend Setup

```bash
# Navigate to backend directory
cd backend

# Copy environment template
cp .env.example .env

# Edit configuration
nano .env

# Install dependencies
pip install -r requirements.txt

# Start server
./start_backend.sh
```

### 2. Frontend Setup

```bash
# Navigate to frontend directory
cd frontend

# Copy environment template
cp .env.example .env

# Edit configuration
nano .env

# Install dependencies
npm install

# Start development server
./start_frontend.sh
```

## Environment-Specific Configurations

### Development Environment
- Debug mode enabled
- Hot reloading enabled
- Detailed logging
- Local file storage

### Production Environment
- Debug mode disabled
- Secure secret keys
- Production URLs
- Optimized performance settings

### Example Production Backend .env
```bash
APP_NAME=ADC Data Validation API
DEBUG=false
RELOAD=false
HOST=0.0.0.0
PORT=8000
SECRET_KEY=your-production-secret-key
BACKEND_URL=https://api.adcvalidation.com
FRONTEND_URL=https://adcvalidation.com
ALLOWED_ORIGINS=https://adcvalidation.com
```

### Example Production Frontend .env
```bash
VITE_APP_NAME=ADC Data Validation UI
VITE_API_BASE_URL=https://api.adcvalidation.com
VITE_DEBUG_MODE=false
VITE_ENABLE_ANALYTICS=true
```

## Configuration Validation

The application includes built-in configuration validation:

### Backend
- Validates required environment variables on startup
- Creates missing directories automatically
- Logs configuration errors

### Frontend
- Validates environment variables during build
- Provides fallback values for missing variables
- Logs configuration in development mode

## Security Considerations

### Secret Keys
- Use strong, unique secret keys for each environment
- Never commit secret keys to version control
- Rotate keys regularly in production

### CORS Configuration
- Restrict ALLOWED_ORIGINS to trusted domains
- Use HTTPS in production
- Validate all origin requests

### File Paths
- Use absolute paths for production
- Ensure proper file permissions
- Validate directory access

## Troubleshooting

### Common Issues

1. **Environment variables not loading**
   - Check file permissions
   - Verify .env file location
   - Ensure no syntax errors in .env file

2. **CORS errors**
   - Verify ALLOWED_ORIGINS includes frontend URL
   - Check protocol (http vs https)
   - Validate port numbers

3. **File path errors**
   - Ensure directories exist
   - Check file permissions
   - Verify absolute vs relative paths

### Debug Commands

```bash
# Check backend configuration
cd backend && python -c "from app.core.config import settings; print(settings.__dict__)"

# Check frontend configuration
cd frontend && npm run build

# Validate environment files
cd backend && python -c "from dotenv import load_dotenv; load_dotenv(); import os; print(os.environ)"
```

## Migration from Hardcoded Values

All previously hardcoded values have been extracted to environment variables:

### Backend Changes
- File paths now configurable via environment
- API URLs externalized
- Security settings configurable
- CORS origins configurable

### Frontend Changes
- API base URL configurable
- Token storage key configurable
- UI settings configurable
- Feature flags available

This makes the application much more flexible and deployment-ready across different environments.
