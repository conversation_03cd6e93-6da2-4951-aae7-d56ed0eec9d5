import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>T<PERSON>le,
  <PERSON>alogContent,
  Dialog<PERSON>ctions,
  Button,
  TextField,
  Box,
  Typography,

  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
} from '@mui/material';
import { Add, Delete } from '@mui/icons-material';
import { useForm, Controller } from 'react-hook-form';

interface ADCData {
  adc_name: string;
  adc_identifier: string;
  target_antigen: string;
  antibody_name: string;
  antibody_type: string;
  linker_type: string;
  payload_name: string;
  payload_type: string;
  drug_antibody_ratio: number | null;
  citations: string[];
}

interface ADCEditorProps {
  open: boolean;
  onClose: () => void;
  onSave: (data: ADCData) => void;
  initialData?: ADCData;
  mode: 'create' | 'edit';
}

const ADCEditor: React.FC<ADCEditorProps> = ({
  open,
  onClose,
  onSave,
  initialData,
  mode,
}) => {
  const [citations, setCitations] = useState<string[]>(initialData?.citations || []);
  const [newCitation, setNewCitation] = useState('');

  const { control, handleSubmit, reset, formState: { errors } } = useForm<ADCData>({
    defaultValues: initialData || {
      adc_name: '',
      adc_identifier: '',
      target_antigen: '',
      antibody_name: '',
      antibody_type: '',
      linker_type: '',
      payload_name: '',
      payload_type: '',
      drug_antibody_ratio: null,
      citations: [],
    },
  });

  const handleAddCitation = () => {
    if (newCitation.trim()) {
      setCitations([...citations, newCitation.trim()]);
      setNewCitation('');
    }
  };

  const handleRemoveCitation = (index: number) => {
    setCitations(citations.filter((_, i) => i !== index));
  };

  const onSubmit = (data: ADCData) => {
    onSave({ ...data, citations });
    handleClose();
  };

  const handleClose = () => {
    reset();
    setCitations([]);
    setNewCitation('');
    onClose();
  };

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {mode === 'create' ? 'Add New ADC' : 'Edit ADC'}
      </DialogTitle>
      <DialogContent>
        <Box component="form" sx={{ mt: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Controller
              name="adc_name"
              control={control}
              rules={{ required: 'ADC name is required' }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="ADC Name"
                  fullWidth
                  error={!!errors.adc_name}
                  helperText={errors.adc_name?.message}
                />
              )}
            />
            <Controller
              name="adc_identifier"
              control={control}
              rules={{ required: 'ADC identifier is required' }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="ADC Identifier"
                  fullWidth
                  error={!!errors.adc_identifier}
                  helperText={errors.adc_identifier?.message}
                />
              )}
            />
            <Controller
              name="target_antigen"
              control={control}
              rules={{ required: 'Target antigen is required' }}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Target Antigen"
                  fullWidth
                  error={!!errors.target_antigen}
                  helperText={errors.target_antigen?.message}
                />
              )}
            />
            <Controller
              name="antibody_name"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Antibody Name"
                  fullWidth
                />
              )}
            />
            <Controller
              name="payload_name"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Payload Name"
                  fullWidth
                />
              )}
            />
            <Controller
              name="drug_antibody_ratio"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Drug-Antibody Ratio"
                  type="number"
                  fullWidth
                  inputProps={{ step: 0.1 }}
                />
              )}
            />

          {/* Citations Section */}
          <Box sx={{ mt: 3 }}>
            <Typography variant="h6" gutterBottom>
              Citations
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
              <TextField
                label="Add Citation"
                value={newCitation}
                onChange={(e) => setNewCitation(e.target.value)}
                fullWidth
                size="small"
                onKeyPress={(e) => {
                  if (e.key === 'Enter') {
                    e.preventDefault();
                    handleAddCitation();
                  }
                }}
              />
              <Button
                variant="outlined"
                onClick={handleAddCitation}
                startIcon={<Add />}
                disabled={!newCitation.trim()}
              >
                Add
              </Button>
            </Box>
            <List dense>
              {citations.map((citation, index) => (
                <ListItem key={index}>
                  <ListItemText
                    primary={citation}
                    primaryTypographyProps={{ variant: 'body2' }}
                  />
                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      onClick={() => handleRemoveCitation(index)}
                      size="small"
                    >
                      <Delete />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Cancel</Button>
        <Button onClick={handleSubmit(onSubmit)} variant="contained">
          {mode === 'create' ? 'Add ADC' : 'Save Changes'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ADCEditor;
