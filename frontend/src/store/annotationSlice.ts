import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { annotationService } from '../services/annotationService';

export interface Annotation {
  publication_id: string;
  annotator_username: string;
  annotation_timestamp?: string;
  status: 'draft' | 'submitted';
  changes: {
    modified_entities: any[];
    new_entities: any[];
    new_citations: any[];
  };
}

interface AnnotationState {
  currentAnnotation: Annotation | null;
  isLoading: boolean;
  error: string | null;
  isSaving: boolean;
}

const initialState: AnnotationState = {
  currentAnnotation: null,
  isLoading: false,
  error: null,
  isSaving: false,
};

// Async thunks
export const fetchAnnotation = createAsyncThunk(
  'annotation/fetchAnnotation',
  async (publicationId: string, { getState }) => {
    const state = getState() as { auth: { token: string } };
    return await annotationService.getAnnotation(publicationId, state.auth.token);
  }
);

export const saveAnnotation = createAsyncThunk(
  'annotation/saveAnnotation',
  async ({ publicationId, changes }: { publicationId: string; changes: any }, { getState }) => {
    const state = getState() as { auth: { token: string } };
    return await annotationService.saveAnnotation(publicationId, changes, state.auth.token);
  }
);

const annotationSlice = createSlice({
  name: 'annotation',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateAnnotation: (state, action) => {
      if (state.currentAnnotation) {
        state.currentAnnotation.changes = action.payload;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Annotation
      .addCase(fetchAnnotation.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAnnotation.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentAnnotation = action.payload;
        state.error = null;
      })
      .addCase(fetchAnnotation.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch annotation';
      })
      // Save Annotation
      .addCase(saveAnnotation.pending, (state) => {
        state.isSaving = true;
        state.error = null;
      })
      .addCase(saveAnnotation.fulfilled, (state) => {
        state.isSaving = false;
        state.error = null;
      })
      .addCase(saveAnnotation.rejected, (state, action) => {
        state.isSaving = false;
        state.error = action.error.message || 'Failed to save annotation';
      });
  },
});

export const { clearError, updateAnnotation } = annotationSlice.actions;
export default annotationSlice.reducer;
