import api from './api';

export interface Annotation {
  publication_id: string;
  annotator_username: string;
  annotation_timestamp?: string;
  status: 'draft' | 'submitted';
  changes: {
    modified_entities: any[];
    new_entities: any[];
    new_citations: any[];
  };
}

class AnnotationService {
  async getAnnotation(publicationId: string, token: string): Promise<Annotation> {
    const response = await api.get(`/api/annotations/${publicationId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  }

  async saveAnnotation(publicationId: string, changes: any, token: string): Promise<any> {
    const response = await api.post(`/api/annotations/${publicationId}`, {
      publication_id: publicationId,
      changes,
    }, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  }

  async submitAnnotation(publicationId: string, token: string): Promise<any> {
    const response = await api.put(`/api/annotations/${publicationId}/submit`, {}, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  }

  async deleteAnnotation(publicationId: string, token: string): Promise<any> {
    const response = await api.delete(`/api/annotations/${publicationId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  }
}

export const annotationService = new AnnotationService();
