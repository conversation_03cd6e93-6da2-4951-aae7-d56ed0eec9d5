"""
Annotation and change tracking models
"""
from typing import List, Dict, Any, Optional, Literal
from pydantic import BaseModel, Field
from datetime import datetime

AnnotationStatus = Literal["draft", "submitted"]
EntityType = Literal["adc", "model", "endpoint"]

class FieldChange(BaseModel):
    """Represents a change to a specific field"""
    old_value: Any = Field(..., description="Original value")
    new_value: Any = Field(..., description="New value")

class EntityChange(BaseModel):
    """Represents changes to an entity"""
    entity_type: EntityType = Field(..., description="Type of entity being changed")
    entity_id: str = Field(..., description="Identifier for the entity")
    field_changes: Dict[str, FieldChange] = Field(..., description="Map of field names to changes")

class NewCitation(BaseModel):
    """Represents a new citation highlight"""
    text: str = Field(..., description="Citation text")
    entity_type: EntityType = Field(..., description="Type of entity this citation supports")
    entity_id: str = Field(..., description="Identifier for the entity")
    start_position: int = Field(..., description="Start position in text")
    end_position: int = Field(..., description="End position in text")

class AnnotationChanges(BaseModel):
    """Container for all changes in an annotation"""
    modified_entities: List[EntityChange] = Field(default=[], description="List of modified entities")
    new_entities: List[Dict[str, Any]] = Field(default=[], description="List of new entities added")
    new_citations: List[NewCitation] = Field(default=[], description="List of new citations added")

class Annotation(BaseModel):
    """Complete annotation record"""
    publication_id: str = Field(..., description="Publication being annotated")
    annotator_username: str = Field(..., description="Username of the annotator")
    annotation_timestamp: datetime = Field(..., description="When the annotation was created/updated")
    status: AnnotationStatus = Field(..., description="Current status of the annotation")
    changes: AnnotationChanges = Field(..., description="All changes made in this annotation")

class AnnotationCreate(BaseModel):
    """Model for creating new annotations"""
    publication_id: str = Field(..., description="Publication being annotated")
    changes: AnnotationChanges = Field(..., description="Changes to be saved")

class AnnotationUpdate(BaseModel):
    """Model for updating existing annotations"""
    changes: AnnotationChanges = Field(..., description="Updated changes")
    status: Optional[AnnotationStatus] = Field(None, description="New status if changing")
