#!/bin/bash

# ADC Data Validation UI - Frontend Startup Script
# This script starts the React frontend development server with environment configuration
# Usage: ./start_frontend.sh [BACKEND_URL]
# Example: ./start_frontend.sh http://backend-hosted-url.com:8000

set -e  # Exit on any error

# Function to show usage
show_usage() {
    echo "Usage: $0 [BACKEND_URL]"
    echo ""
    echo "Arguments:"
    echo "  BACKEND_URL    Optional. Backend API URL (default: http://localhost:8000)"
    echo ""
    echo "Examples:"
    echo "  $0                                          # Use default localhost backend"
    echo "  $0 http://backend-hosted-url.com:8000      # Use custom backend URL"
    echo "  $0 https://api.example.com                 # Use HTTPS backend"
    echo ""
}

# Check for help flag
if [[ "$1" == "-h" || "$1" == "--help" ]]; then
    show_usage
    exit 0
fi

# Get backend URL from parameter or use default
BACKEND_URL="${1:-http://localhost:8000}"

# Validate backend URL format
if [[ ! "$BACKEND_URL" =~ ^https?:// ]]; then
    echo "❌ Error: Backend URL must start with http:// or https://"
    echo "   Provided: $BACKEND_URL"
    show_usage
    exit 1
fi

echo "🚀 Starting ADC Data Validation UI Frontend..."
echo "🔗 Backend URL: $BACKEND_URL"

# Change to frontend directory
cd "$(dirname "$0")/frontend"

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Copying from .env.example..."
    cp .env.example .env
    echo "✅ Created .env file."
fi

# Update backend URL in .env file
echo "🔧 Updating backend URL in .env file..."
if grep -q "VITE_API_BASE_URL=" .env; then
    # Replace existing VITE_API_BASE_URL
    sed -i "s|VITE_API_BASE_URL=.*|VITE_API_BASE_URL=$BACKEND_URL|" .env
else
    # Add VITE_API_BASE_URL if it doesn't exist
    echo "VITE_API_BASE_URL=$BACKEND_URL" >> .env
fi
echo "✅ Backend URL updated to: $BACKEND_URL"

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
else
    echo "📦 Dependencies already installed."
fi

# Load environment variables for display
if [ -f .env ]; then
    echo "📋 Environment configuration:"
    echo "   App Name: $(grep VITE_APP_NAME .env | cut -d '=' -f2)"
    echo "   API URL: $(grep VITE_API_BASE_URL .env | cut -d '=' -f2)"
    echo "   Debug Mode: $(grep VITE_DEBUG_MODE .env | cut -d '=' -f2)"
    echo "   Server Host: $(grep VITE_DEV_SERVER_HOST .env | cut -d '=' -f2)"
    echo "   Server Port: $(grep VITE_DEV_SERVER_PORT .env | cut -d '=' -f2)"
fi

echo "🌐 Starting Vite development server..."
echo "   Note: Server will be accessible from external hosts for port forwarding"

# Start the development server
npm run dev
