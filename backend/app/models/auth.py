"""
Authentication and user models
"""
from typing import Optional, Literal
from pydantic import BaseModel, Field
from datetime import datetime

# User roles
UserRole = Literal["admin", "annotator", "viewer"]

class UserBase(BaseModel):
    """Base user model"""
    username: str = Field(..., description="Username for authentication")
    role: UserRole = Field(..., description="User role determining permissions")

class UserCreate(UserBase):
    """User creation model"""
    password: str = Field(..., min_length=6, description="User password")

class UserInDB(UserBase):
    """User model as stored in database"""
    password: str = Field(..., description="Hashed password")
    created_at: Optional[datetime] = Field(None, description="Account creation timestamp")
    last_login: Optional[datetime] = Field(None, description="Last login timestamp")

class User(UserBase):
    """User model for API responses (no password)"""
    created_at: Optional[datetime] = Field(None, description="Account creation timestamp")
    last_login: Optional[datetime] = Field(None, description="Last login timestamp")

class Token(BaseModel):
    """JWT token response"""
    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")

class TokenData(BaseModel):
    """Token payload data"""
    username: Optional[str] = None
    role: Optional[UserRole] = None

class LoginRequest(BaseModel):
    """Login request model"""
    username: str = Field(..., description="Username")
    password: str = Field(..., description="Password")
