#!/bin/bash

# ADC Data Validation API - Backend Startup Script
# This script starts the FastAPI backend server with environment configuration

set -e  # Exit on any error

echo "🚀 Starting ADC Data Validation API Backend..."

# Change to backend directory
cd "$(dirname "$0")/backend"
echo "Current directory: $(pwd)"

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Copying from .env.example..."
    cp .env.example .env
    echo "✅ Created .env file. Please review and update the configuration."
fi

# Load environment variables
if [ -f .env ]; then
    echo "📋 Loading environment variables from .env file..."
    set -a                  # automatically export all variables
    source .env            # source the .env file
    set +a                 # stop automatically exporting
fi

# Ensure Python 3.10 is installed via conda
if ! conda info --envs | grep -q "adc-py310"; then
    echo "🔧 Creating conda environment 'adc-py310' with Python 3.10..."
    conda create -y -n adc-py310 python=3.10
fi
echo "📦 Activating conda environment 'adc-py310'..."
source "$(conda info --base)/etc/profile.d/conda.sh"
conda activate adc-py310

echo "📦 Installing/updating dependencies..."
pip install --user -r requirements.txt

# Ensure data directories exist
echo "📁 Ensuring data directories exist..."
mkdir -p "${DATA_ROOT}/publications"
mkdir -p "${DATA_ROOT}/annotations"
mkdir -p "${DATA_ROOT}/users"

# Copy sample data if users file doesn't exist
if [ ! -f "${USERS_FILE}" ]; then
    echo "👥 Setting up initial user data..."
    cp "${SAMPLE_DATA_DIR}/sample_users.json" "${USERS_FILE}"
fi

# Copy sample publications if directory is empty
if [ ! "$(ls -A "${PUBLICATIONS_DIR}")" ]; then
    echo "📚 Setting up sample publication data..."
    cp "${SAMPLE_DATA_DIR}"/* "${PUBLICATIONS_DIR}/"
fi

echo "🌐 Starting FastAPI server..."
echo "   Host: ${HOST:-0.0.0.0}"
echo "   Port: ${PORT:-8000}"
echo "   Debug: ${DEBUG:-true}"
echo "   Reload: ${RELOAD:-true}"

# Start the server
PYTHONPATH=. uvicorn app.main:app \
    --host "${HOST:-0.0.0.0}" \
    --port "${PORT:-8000}" 
