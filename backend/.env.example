# ADC Data Validation API - Environment Configuration
# Copy this file to .env and update the values for your environment

# Application Settings
APP_NAME=ADC Data Validation API
APP_VERSION=1.0.0
DEBUG=true
RELOAD=true

# Server Configuration
HOST=0.0.0.0
PORT=8000

# Base Paths
WORKSPACE_ROOT=/mnt/data/ADC_Data_Center/adc_user_interface
DATA_ROOT=${WORKSPACE_ROOT}/data
PUBLICATIONS_DIR=${DATA_ROOT}/publications
ANNOTATIONS_DIR=${DATA_ROOT}/annotations
USERS_FILE=${DATA_ROOT}/users/users.json
SAMPLE_DATA_DIR=${WORKSPACE_ROOT}/sample_data

# API URLs
BACKEND_URL=http://localhost:8000
FRONTEND_URL=http://localhost:3000

# Security Settings
SECRET_KEY=dev-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=1440
ALGORITHM=HS256

# CORS Settings (comma-separated list)
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:5173,http://127.0.0.1:5173

# Database Settings (for future use)
# DATABASE_URL=sqlite:///./adc_validation.db
# DATABASE_ECHO=false

# Logging Settings
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# File Upload Settings
MAX_FILE_SIZE_MB=100
ALLOWED_FILE_TYPES=json,md,txt,csv

# Cache Settings
CACHE_TTL_SECONDS=3600
ENABLE_CACHE=true

# External API Settings (for future integrations)
# EXTERNAL_API_URL=https://api.example.com
# EXTERNAL_API_KEY=your-api-key-here
# EXTERNAL_API_TIMEOUT=30

# Email Settings (for notifications)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your-app-password
# EMAIL_FROM=<EMAIL>
