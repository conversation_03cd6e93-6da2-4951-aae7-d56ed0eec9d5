/**
 * Application configuration loaded from environment variables
 * All environment variables must be prefixed with VITE_ to be accessible in the browser
 */

export interface AppConfig {
  // Application settings
  appName: string;
  appVersion: string;
  appDescription: string;

  // API configuration
  apiBaseUrl: string;
  apiTimeout: number;

  // Authentication settings
  tokenStorageKey: string;
  tokenRefreshThreshold: number;

  // UI configuration
  defaultPageSize: number;
  maxPageSize: number;
  debounceDelay: number;

  // Feature flags
  enableDarkMode: boolean;
  enableExport: boolean;
  enableCollaboration: boolean;
  enableAnalytics: boolean;

  // Development settings
  debugMode: boolean;
  showDevTools: boolean;
  mockApi: boolean;

  // File upload settings
  maxFileSizeMB: number;
  allowedFileTypes: string[];

  // Citation settings
  maxCitationLength: number;
  minCitationLength: number;
  citationHighlightColors: string[];

  // Table settings
  defaultRowsPerPage: number;
  maxRowsPerPage: number;

  // Notification settings
  notificationTimeout: number;
  enableSoundNotifications: boolean;

  // Performance settings
  enableVirtualScrolling: boolean;
  lazyLoadThreshold: number;
}

const getEnvVar = (key: string, defaultValue: string = ''): string => {
  return import.meta.env[key] || defaultValue;
};

const getEnvNumber = (key: string, defaultValue: number): number => {
  const value = import.meta.env[key];
  return value ? parseInt(value, 10) : defaultValue;
};

const getEnvBoolean = (key: string, defaultValue: boolean): boolean => {
  const value = import.meta.env[key];
  return value ? value.toLowerCase() === 'true' : defaultValue;
};

const getEnvArray = (key: string, defaultValue: string[] = []): string[] => {
  const value = import.meta.env[key];
  return value ? value.split(',').map((item: string) => item.trim()) : defaultValue;
};

export const config: AppConfig = {
  // Application settings
  appName: getEnvVar('VITE_APP_NAME', 'ADC Data Validation UI'),
  appVersion: getEnvVar('VITE_APP_VERSION', '1.0.0'),
  appDescription: getEnvVar('VITE_APP_DESCRIPTION', 'Expert validation interface for ADC data extraction'),

  // API configuration
  apiBaseUrl: getEnvVar('VITE_API_BASE_URL', 'http://localhost:8000'),
  apiTimeout: getEnvNumber('VITE_API_TIMEOUT', 30000),

  // Authentication settings
  tokenStorageKey: getEnvVar('VITE_TOKEN_STORAGE_KEY', 'adc_validation_token'),
  tokenRefreshThreshold: getEnvNumber('VITE_TOKEN_REFRESH_THRESHOLD', 300000),

  // UI configuration
  defaultPageSize: getEnvNumber('VITE_DEFAULT_PAGE_SIZE', 10),
  maxPageSize: getEnvNumber('VITE_MAX_PAGE_SIZE', 100),
  debounceDelay: getEnvNumber('VITE_DEBOUNCE_DELAY', 300),

  // Feature flags
  enableDarkMode: getEnvBoolean('VITE_ENABLE_DARK_MODE', true),
  enableExport: getEnvBoolean('VITE_ENABLE_EXPORT', true),
  enableCollaboration: getEnvBoolean('VITE_ENABLE_COLLABORATION', false),
  enableAnalytics: getEnvBoolean('VITE_ENABLE_ANALYTICS', false),

  // Development settings
  debugMode: getEnvBoolean('VITE_DEBUG_MODE', true),
  showDevTools: getEnvBoolean('VITE_SHOW_DEV_TOOLS', true),
  mockApi: getEnvBoolean('VITE_MOCK_API', false),

  // File upload settings
  maxFileSizeMB: getEnvNumber('VITE_MAX_FILE_SIZE_MB', 50),
  allowedFileTypes: getEnvArray('VITE_ALLOWED_FILE_TYPES', ['json', 'md', 'txt', 'csv', 'pdf']),

  // Citation settings
  maxCitationLength: getEnvNumber('VITE_MAX_CITATION_LENGTH', 500),
  minCitationLength: getEnvNumber('VITE_MIN_CITATION_LENGTH', 10),
  citationHighlightColors: getEnvArray('VITE_CITATION_HIGHLIGHT_COLORS', ['blue', 'green', 'orange', 'purple', 'red']),

  // Table settings
  defaultRowsPerPage: getEnvNumber('VITE_DEFAULT_ROWS_PER_PAGE', 25),
  maxRowsPerPage: getEnvNumber('VITE_MAX_ROWS_PER_PAGE', 100),

  // Notification settings
  notificationTimeout: getEnvNumber('VITE_NOTIFICATION_TIMEOUT', 5000),
  enableSoundNotifications: getEnvBoolean('VITE_ENABLE_SOUND_NOTIFICATIONS', false),

  // Performance settings
  enableVirtualScrolling: getEnvBoolean('VITE_ENABLE_VIRTUAL_SCROLLING', true),
  lazyLoadThreshold: getEnvNumber('VITE_LAZY_LOAD_THRESHOLD', 100),
};

// Development logging
if (config.debugMode) {
  console.log('🔧 Application Configuration:', config);
}

export default config;
