import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import {
  Container,
  Typography,
  Card,
  CardContent,
  CardActions,
  Button,

  Box,
  Chip,
  CircularProgress,
  Alert,
} from '@mui/material';
import type { RootState, AppDispatch } from '../store';
import { fetchPublications } from '../store/publicationSlice';
import { getProfile } from '../store/authSlice';
import Header from '../components/common/Header';

const DashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch<AppDispatch>();
  const { publications, isLoading, error } = useSelector((state: RootState) => state.publication);
  const { user, token } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    if (token && !user) {
      dispatch(getProfile());
    }
  }, [dispatch, token, user]);

  useEffect(() => {
    if (token) {
      dispatch(fetchPublications());
    }
  }, [dispatch, token]);

  const handleOpenPublication = (publicationId: string) => {
    navigate(`/annotation/${publicationId}`);
  };

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="100vh">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <>
      <Header />
      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Publications Dashboard
        </Typography>
        <Typography variant="body1" color="textSecondary" gutterBottom>
          Select a publication to view and annotate ADC extraction data
        </Typography>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))', gap: 3, mt: 2 }}>
          {publications.map((publication) => (
            <Card key={publication.publication_id} sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
              <CardContent sx={{ flexGrow: 1 }}>
                <Typography variant="h6" gutterBottom>
                  {publication.title}
                </Typography>
                <Box sx={{ mb: 2 }}>
                  <Chip
                    label={`${publication.adc_count} ADCs`}
                    size="small"
                    color="primary"
                    sx={{ mr: 1, mb: 1 }}
                  />
                  <Chip
                    label={`${publication.model_count} Models`}
                    size="small"
                    color="secondary"
                    sx={{ mr: 1, mb: 1 }}
                  />
                  <Chip
                    label={`${publication.combination_count} Combinations`}
                    size="small"
                    color="default"
                    sx={{ mr: 1, mb: 1 }}
                  />
                </Box>
              </CardContent>
              <CardActions>
                <Button
                  size="small"
                  variant="contained"
                  onClick={() => handleOpenPublication(publication.publication_id)}
                  fullWidth
                >
                  Open for Annotation
                </Button>
              </CardActions>
            </Card>
          ))}
        </Box>

        {publications.length === 0 && !isLoading && (
          <Box textAlign="center" sx={{ mt: 4 }}>
            <Typography variant="h6" color="textSecondary">
              No publications available
            </Typography>
          </Box>
        )}
      </Container>
    </>
  );
};

export default DashboardPage;
