import api from './api';

export interface LoginResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
}

export interface User {
  username: string;
  role: 'admin' | 'annotator' | 'viewer';
  created_at?: string;
  last_login?: string;
}

class AuthService {
  async login(username: string, password: string): Promise<LoginResponse> {
    const response = await api.post('/api/auth/login', {
      username,
      password,
    });
    return response.data;
  }

  async getProfile(token: string): Promise<User> {
    const response = await api.get('/api/auth/profile', {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    return response.data;
  }

  async logout(): Promise<void> {
    await api.post('/api/auth/logout');
  }
}

export const authService = new AuthService();
