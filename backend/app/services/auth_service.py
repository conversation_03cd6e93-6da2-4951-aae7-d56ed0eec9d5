"""
Authentication service for user management
"""
import json
from typing import List, Optional
from datetime import datetime
from app.models.auth import User, UserInDB, UserCreate
from app.core.config import settings
from app.core.security import verify_password, get_password_hash
from app.services.file_service import file_service

class AuthService:
    """Service for user authentication and management"""

    async def get_users(self) -> List[UserInDB]:
        """Load all users from file"""
        print(f"Loading users from: {settings.USERS_FILE}")
        users_data = await file_service.read_json_file(settings.USERS_FILE)
        print(f"Users data loaded: {users_data}")
        if not users_data:
            return []

        users = []
        for user_data in users_data:
            # Convert to UserInDB model, handling missing fields
            user = UserInDB(
                username=user_data["username"],
                password=user_data["password"],
                role=user_data["role"],
                created_at=user_data.get("created_at"),
                last_login=user_data.get("last_login")
            )
            users.append(user)

        print(f"Loaded {len(users)} users")
        return users

    async def get_user_by_username(self, username: str) -> Optional[UserInDB]:
        """Get user by username"""
        users = await self.get_users()
        for user in users:
            if user.username == username:
                return user
        return None

    async def authenticate_user(self, username: str, password: str) -> Optional[UserInDB]:
        """Authenticate user with username and password"""
        user = await self.get_user_by_username(username)
        if not user:
            return None

        # For development, check if password is already hashed
        if password == user.password:
            # Plain text password match (for sample data)
            return user
        elif verify_password(password, user.password):
            # Hashed password match
            return user

        return None

    async def create_user(self, user_create: UserCreate) -> Optional[UserInDB]:
        """Create a new user"""
        # Check if user already exists
        existing_user = await self.get_user_by_username(user_create.username)
        if existing_user:
            return None

        # Hash password
        hashed_password = get_password_hash(user_create.password)

        # Create user
        new_user = UserInDB(
            username=user_create.username,
            password=hashed_password,
            role=user_create.role,
            created_at=datetime.utcnow()
        )

        # Load existing users
        users = await self.get_users()
        users.append(new_user)

        # Save to file
        users_data = [user.dict() for user in users]
        success = await file_service.write_json_file(settings.USERS_FILE, users_data)

        return new_user if success else None

    async def update_last_login(self, username: str) -> bool:
        """Update user's last login timestamp"""
        users = await self.get_users()

        for user in users:
            if user.username == username:
                user.last_login = datetime.utcnow()
                break
        else:
            return False

        # Save updated users
        users_data = [user.dict() for user in users]
        return await file_service.write_json_file(settings.USERS_FILE, users_data)

# Create service instance
auth_service = AuthService()
