"""
Publication and data structure models
"""
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from app.models.extraction import Antibody<PERSON>rugConjugate, ExperimentalModel

class ADCModelCombination(BaseModel):
    """Single ADC-Model combination with endpoints"""
    adc_data: AntibodyDrugConjugate = Field(..., description="ADC information")
    experimental_model_data: ExperimentalModel = Field(..., description="Experimental model information")
    endpoints: List[Dict[str, Any]] = Field(..., description="List of endpoint measurements")

class PublicationData(BaseModel):
    """Complete publication data structure"""
    publication_id: str = Field(..., description="Unique publication identifier")
    title: str = Field(..., description="Publication title")
    fulltext_markdown: Optional[str] = Field(None, description="Full text content in markdown format")
    extraction_data: List[ADCModelCombination] = Field(..., description="List of ADC-model combinations")

class PublicationSummary(BaseModel):
    """Summary information for publication listing"""
    publication_id: str = Field(..., description="Unique publication identifier")
    title: str = Field(..., description="Publication title")
    adc_count: int = Field(..., description="Number of unique ADCs")
    model_count: int = Field(..., description="Number of unique models")
    combination_count: int = Field(..., description="Number of ADC-model combinations")

class ADCSummary(BaseModel):
    """Summary of ADC information"""
    adc_name: str = Field(..., description="ADC name")
    adc_identifier: str = Field(..., description="ADC identifier")
    target_antigen: str = Field(..., description="Target antigen")
    combination_count: int = Field(..., description="Number of combinations with this ADC")

class ModelSummary(BaseModel):
    """Summary of experimental model information"""
    model_identifier: str = Field(..., description="Model identifier")
    model_type: str = Field(..., description="Model type")
    primary_cancer_type: str = Field(..., description="Primary cancer type")
    combination_count: int = Field(..., description="Number of combinations with this model")
