import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // Load environment variables
  const env = loadEnv(mode, process.cwd(), '')

  return {
    plugins: [react()],
    server: {
      host: env.VITE_DEV_SERVER_HOST || '0.0.0.0', // Allow external connections
      port: parseInt(env.VITE_DEV_SERVER_PORT) || 5173, // Default Vite port
      strictPort: false, // Allow fallback to other ports if specified port is busy
      open: false, // Don't auto-open browser in headless environments
    },
    preview: {
      host: env.VITE_DEV_SERVER_HOST || '0.0.0.0', // Also configure preview server for production builds
      port: parseInt(env.VITE_DEV_SERVER_PORT) + 1000 || 4173, // Preview on different port
      strictPort: false,
    },
  }
})
