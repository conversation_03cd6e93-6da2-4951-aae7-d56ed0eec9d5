import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { publicationService } from '../services/publicationService';

export interface PublicationSummary {
  publication_id: string;
  title: string;
  adc_count: number;
  model_count: number;
  combination_count: number;
}

export interface PublicationData {
  publication_id: string;
  title: string;
  fulltext_markdown?: string;
  extraction_data: any[];
}

interface PublicationState {
  publications: PublicationSummary[];
  currentPublication: PublicationData | null;
  isLoading: boolean;
  error: string | null;
}

const initialState: PublicationState = {
  publications: [],
  currentPublication: null,
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchPublications = createAsyncThunk(
  'publication/fetchPublications',
  async (_, { getState }) => {
    const state = getState() as { auth: { token: string } };
    return await publicationService.getPublications(state.auth.token);
  }
);

export const fetchPublication = createAsyncThunk(
  'publication/fetchPublication',
  async (publicationId: string, { getState }) => {
    const state = getState() as { auth: { token: string } };
    return await publicationService.getPublication(publicationId, state.auth.token);
  }
);

const publicationSlice = createSlice({
  name: 'publication',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentPublication: (state) => {
      state.currentPublication = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Publications
      .addCase(fetchPublications.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPublications.fulfilled, (state, action) => {
        state.isLoading = false;
        state.publications = action.payload;
        state.error = null;
      })
      .addCase(fetchPublications.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch publications';
      })
      // Fetch Publication
      .addCase(fetchPublication.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchPublication.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentPublication = action.payload;
        state.error = null;
      })
      .addCase(fetchPublication.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Failed to fetch publication';
      });
  },
});

export const { clearError, clearCurrentPublication } = publicationSlice.actions;
export default publicationSlice.reducer;
