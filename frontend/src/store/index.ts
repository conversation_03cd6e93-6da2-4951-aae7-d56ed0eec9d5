import { configureStore } from '@reduxjs/toolkit';
import authSlice from './authSlice';
import publicationSlice from './publicationSlice';
import annotationSlice from './annotationSlice';

export const store = configureStore({
  reducer: {
    auth: authSlice,
    publication: publicationSlice,
    annotation: annotationSlice,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
