# ADC Data Validation UI - Frontend Environment Configuration
# Copy this file to .env and update the values for your environment

# Application Settings
VITE_APP_NAME=ADC Data Validation UI
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Expert validation interface for ADC data extraction

# API Configuration
# Note: VITE_API_BASE_URL can be overridden by passing a URL to start_frontend.sh
VITE_API_BASE_URL=http://localhost:8000
VITE_API_TIMEOUT=30000

# Authentication Settings
VITE_TOKEN_STORAGE_KEY=adc_validation_token
VITE_TOKEN_REFRESH_THRESHOLD=300000

# UI Configuration
VITE_DEFAULT_PAGE_SIZE=10
VITE_MAX_PAGE_SIZE=100
VITE_DEBOUNCE_DELAY=300

# Feature Flags
VITE_ENABLE_DARK_MODE=true
VITE_ENABLE_EXPORT=true
VITE_ENABLE_COLLABORATION=false
VITE_ENABLE_ANALYTICS=false

# Development Settings
VITE_DEBUG_MODE=true
VITE_SHOW_DEV_TOOLS=true
VITE_MOCK_API=false

# Server Settings
VITE_DEV_SERVER_HOST=0.0.0.0
VITE_DEV_SERVER_PORT=5173

# File Upload Settings
VITE_MAX_FILE_SIZE_MB=50
VITE_ALLOWED_FILE_TYPES=json,md,txt,csv,pdf

# Citation Settings
VITE_MAX_CITATION_LENGTH=500
VITE_MIN_CITATION_LENGTH=10
VITE_CITATION_HIGHLIGHT_COLORS=blue,green,orange,purple,red

# Table Settings
VITE_DEFAULT_ROWS_PER_PAGE=25
VITE_MAX_ROWS_PER_PAGE=100

# Notification Settings
VITE_NOTIFICATION_TIMEOUT=5000
VITE_ENABLE_SOUND_NOTIFICATIONS=false

# Performance Settings
VITE_ENABLE_VIRTUAL_SCROLLING=true
VITE_LAZY_LOAD_THRESHOLD=100

# External Services (for future use)
# VITE_ANALYTICS_ID=your-analytics-id
# VITE_SENTRY_DSN=your-sentry-dsn
# VITE_SUPPORT_EMAIL=<EMAIL>
