"""
Publication data service for loading and managing publication data
"""
import json
from typing import List, Optional, Dict, Any
from pathlib import Path
from app.models.publication import (
    PublicationData, PublicationSummary, ADCSummary, ModelSummary, ADCModelCombination
)
from app.core.config import settings
from app.services.file_service import file_service

class PublicationService:
    """Service for publication data management"""
    
    async def get_publications(self) -> List[PublicationSummary]:
        """Get list of all publications with summary information"""
        publications = []
        
        # For now, we'll use the sample data
        # In the future, this could scan the publications directory
        sample_file = settings.PUBLICATIONS_DIR / "sample_extraction_output.json"
        
        if sample_file.exists():
            # Create a publication from sample data
            extraction_data = await file_service.read_json_file(sample_file)
            if extraction_data:
                # Get unique ADCs and models
                unique_adcs = self._get_unique_adcs(extraction_data)
                unique_models = self._get_unique_models(extraction_data)
                
                publication = PublicationSummary(
                    publication_id="sample_publication_001",
                    title="Promiximab-duocarmycin, a new CD56 antibody-drug conjugates",
                    adc_count=len(unique_adcs),
                    model_count=len(unique_models),
                    combination_count=len(extraction_data)
                )
                publications.append(publication)
        
        return publications
    
    async def get_publication(self, publication_id: str) -> Optional[PublicationData]:
        """Get complete publication data by ID"""
        if publication_id == "sample_publication_001":
            # Load sample data
            extraction_file = settings.PUBLICATIONS_DIR / "sample_extraction_output.json"
            fulltext_file = settings.PUBLICATIONS_DIR / "sample_publication_fulltext.md"
            
            extraction_data = await file_service.read_json_file(extraction_file)
            fulltext = await file_service.read_text_file(fulltext_file)
            
            if extraction_data:
                # Convert to ADCModelCombination objects
                combinations = []
                for combo_data in extraction_data:
                    combination = ADCModelCombination(**combo_data)
                    combinations.append(combination)
                
                return PublicationData(
                    publication_id=publication_id,
                    title="Promiximab-duocarmycin, a new CD56 antibody-drug conjugates",
                    fulltext_markdown=fulltext,
                    extraction_data=combinations
                )
        
        return None
    
    async def get_publication_adcs(self, publication_id: str) -> List[ADCSummary]:
        """Get unique ADCs for a publication"""
        publication = await self.get_publication(publication_id)
        if not publication:
            return []
        
        unique_adcs = self._get_unique_adcs([combo.dict() for combo in publication.extraction_data])
        
        adc_summaries = []
        for adc_data in unique_adcs:
            # Count combinations for this ADC
            combo_count = sum(
                1 for combo in publication.extraction_data
                if combo.adc_data.adc_identifier == adc_data["adc_identifier"]
            )
            
            summary = ADCSummary(
                adc_name=adc_data["adc_name"],
                adc_identifier=adc_data["adc_identifier"],
                target_antigen=adc_data["target_antigen"],
                combination_count=combo_count
            )
            adc_summaries.append(summary)
        
        return adc_summaries
    
    async def get_publication_models(self, publication_id: str) -> List[ModelSummary]:
        """Get unique models for a publication"""
        publication = await self.get_publication(publication_id)
        if not publication:
            return []
        
        unique_models = self._get_unique_models([combo.dict() for combo in publication.extraction_data])
        
        model_summaries = []
        for model_data in unique_models:
            # Count combinations for this model
            combo_count = sum(
                1 for combo in publication.extraction_data
                if combo.experimental_model_data.model_identifier == model_data["model_identifier"]
            )
            
            summary = ModelSummary(
                model_identifier=model_data["model_identifier"],
                model_type=model_data["model_type"],
                primary_cancer_type=model_data["primary_cancer_type"],
                combination_count=combo_count
            )
            model_summaries.append(summary)
        
        return model_summaries
    
    def _get_unique_adcs(self, extraction_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Extract unique ADCs from extraction data"""
        seen_adcs = {}
        for combo in extraction_data:
            adc_id = combo["adc_data"]["adc_identifier"]
            if adc_id not in seen_adcs:
                seen_adcs[adc_id] = combo["adc_data"]
        
        return list(seen_adcs.values())
    
    def _get_unique_models(self, extraction_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Extract unique models from extraction data"""
        seen_models = {}
        for combo in extraction_data:
            model_id = combo["experimental_model_data"]["model_identifier"]
            if model_id not in seen_models:
                seen_models[model_id] = combo["experimental_model_data"]
        
        return list(seen_models.values())

# Create service instance
publication_service = PublicationService()
