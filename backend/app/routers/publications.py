"""
Publications router for publication data access
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from app.models.auth import User
from app.models.publication import PublicationSummary, PublicationData, ADCSummary, ModelSummary
from app.services.publication_service import publication_service
from app.routers.auth import get_current_user

router = APIRouter()

@router.get("/", response_model=List[PublicationSummary])
async def get_publications(current_user: User = Depends(get_current_user)):
    """Get list of all publications with summary information"""
    return await publication_service.get_publications()

@router.get("/{publication_id}", response_model=PublicationData)
async def get_publication(
    publication_id: str,
    current_user: User = Depends(get_current_user)
):
    """Get complete publication data by ID"""
    publication = await publication_service.get_publication(publication_id)
    if not publication:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Publication not found"
        )
    return publication

@router.get("/{publication_id}/adcs", response_model=List[ADCSummary])
async def get_publication_adcs(
    publication_id: str,
    current_user: User = Depends(get_current_user)
):
    """Get unique ADCs for a publication"""
    adcs = await publication_service.get_publication_adcs(publication_id)
    if not adcs:
        # Check if publication exists
        publication = await publication_service.get_publication(publication_id)
        if not publication:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Publication not found"
            )
    return adcs

@router.get("/{publication_id}/models", response_model=List[ModelSummary])
async def get_publication_models(
    publication_id: str,
    current_user: User = Depends(get_current_user)
):
    """Get unique models for a publication"""
    models = await publication_service.get_publication_models(publication_id)
    if not models:
        # Check if publication exists
        publication = await publication_service.get_publication(publication_id)
        if not publication:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Publication not found"
            )
    return models

@router.get("/{publication_id}/fulltext")
async def get_publication_fulltext(
    publication_id: str,
    current_user: User = Depends(get_current_user)
):
    """Get publication fulltext markdown"""
    publication = await publication_service.get_publication(publication_id)
    if not publication:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Publication not found"
        )
    
    return {
        "publication_id": publication_id,
        "fulltext_markdown": publication.fulltext_markdown
    }
